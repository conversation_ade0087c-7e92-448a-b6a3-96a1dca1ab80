package com.oto.front.config.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.oto.common.core.constant.CacheNames;
import com.oto.common.domain.OtoConfig;
import com.oto.front.config.mapper.OtoConfigMapper;
import com.oto.constantandenums.enums.OtoConfigKeyEnums;
import com.oto.otoConfigApi.OtoConfigApiService;
import com.oto.otoConfigApi.dto.OtoConfigDTO;
import lombok.RequiredArgsConstructor;
import com.oto.common.core.utils.MapstructUtils;
import com.oto.common.core.utils.StringUtils;
import com.oto.common.redis.utils.CacheUtils;
import org.springframework.stereotype.Service;

/**
 * oto参数配置表 API服务实现类
 * 迁移自oto-front-manage模块，现在由oto-front-system-config模块提供
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Service
@RequiredArgsConstructor
public class OtoConfigApiServiceImpl implements OtoConfigApiService {

    private final OtoConfigMapper otoConfigMapper;

    /**
     * 根据键名查询参数配置
     *
     * @param configKey 参数键名
     * @return 参数配置信息
     */
    @Override
    public OtoConfigDTO selectOtoConfigByKey(String configKey) {
        LambdaQueryWrapper<OtoConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OtoConfig::getConfigKey, configKey);
        OtoConfig otoConfig = otoConfigMapper.selectOne(queryWrapper);
        return MapstructUtils.convert(otoConfig, OtoConfigDTO.class);
    }

    /**
     * 根据键名查询参数配置值（开关类型）
     *
     * @param configKey 参数键名
     * @return 参数键值（boolean类型）
     */
    @Override
    public Boolean getEnableByConfigKey(OtoConfigKeyEnums configKey) {
        String key = configKey.getKey();
        if (StringUtils.isBlank(key)) {
            return true; // 默认开启
        }

        // 先从缓存获取
        Object cacheValue = CacheUtils.get(CacheNames.OTO_CONFIG, key);
        if (cacheValue != null) {
            if (cacheValue instanceof Boolean) {
                return (Boolean) cacheValue;
            }
            return Boolean.TRUE.toString().equalsIgnoreCase(String.valueOf(cacheValue));
        }

        // 如果缓存中没有，则查询数据库
        LambdaQueryWrapper<OtoConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OtoConfig::getConfigKey, key);
        OtoConfig otoConfig = otoConfigMapper.selectOne(queryWrapper);
        Boolean result = true; // 默认值为 true
        if (otoConfig != null && otoConfig.getConfigValue() != null) {
            result = Boolean.TRUE.toString().equalsIgnoreCase(otoConfig.getConfigValue());
        }

        // 存入缓存
        CacheUtils.put(CacheNames.OTO_CONFIG, key, result);
        return result;
    }
}
