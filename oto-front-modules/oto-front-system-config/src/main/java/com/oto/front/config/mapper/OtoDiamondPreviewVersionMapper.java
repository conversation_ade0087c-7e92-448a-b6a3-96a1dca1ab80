package com.oto.front.config.mapper;

import com.oto.common.domain.OtoDiamondPreviewVersion;
import com.oto.common.domain.vo.OtoDiamondPreviewVersionVo;
import com.oto.common.mybatis.core.mapper.BaseMapperPlus;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 金刚位预览版本管理Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface OtoDiamondPreviewVersionMapper extends BaseMapperPlus<OtoDiamondPreviewVersion, OtoDiamondPreviewVersionVo> {

    /**
     * 获取当前发布版本
     *
     * @return 当前版本
     */
    OtoDiamondPreviewVersionVo selectCurrentVersion();

    /**
     * 根据版本编码查询
     *
     * @param versionCode 版本编码
     * @return 版本信息
     */
    OtoDiamondPreviewVersionVo selectByVersionCode(@Param("versionCode") String versionCode);

    /**
     * 更新当前发布版本的停止时间
     *
     * @param stopTime 停止时间
     * @return 更新数量
     */
    int updateStopTimeForCurrentVersion(@Param("stopTime") LocalDateTime stopTime);

    /**
     * 更新当前版本状态（将所有版本设为非当前版本）
     *
     * @return 更新数量
     */
    int clearCurrentStatus();

    /**
     * 设置指定版本为当前版本
     *
     * @param versionCode 版本编码
     * @return 更新数量
     */
    int setCurrentVersion(@Param("versionCode") String versionCode);

    /**
     * 更新版本状态
     *
     * @param versionCode 版本编码
     * @param status 状态
     * @return 更新数量
     */
    int updateVersionStatus(@Param("versionCode") String versionCode, @Param("status") Integer status);

    /**
     * 批量更新版本状态
     *
     * @param ids ID列表
     * @param status 状态
     * @return 更新数量
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") Integer status);

    /**
     * 获取指定状态的版本列表
     *
     * @param status 状态
     * @return 版本列表
     */
    List<OtoDiamondPreviewVersionVo> selectByStatus(@Param("status") Integer status);

    /**
     * 检查版本编码是否存在
     *
     * @param versionCode 版本编码
     * @param excludeId 排除的ID
     * @return 数量
     */
    Integer countByVersionCode(@Param("versionCode") String versionCode, @Param("excludeId") Long excludeId);

    /**
     * 获取最新的版本编码
     *
     * @return 版本编码
     */
    String selectLatestVersionCode();

    /**
     * 统计各状态版本数量
     *
     * @return 状态统计
     */
    List<Map<String, Object>> countByStatusGroup();

    /**
     * 删除过期的草稿版本（保留最近N个）
     *
     * @param keepCount 保留数量
     * @return 删除数量
     */
    int deleteExpiredDrafts(@Param("keepCount") Integer keepCount);

}
