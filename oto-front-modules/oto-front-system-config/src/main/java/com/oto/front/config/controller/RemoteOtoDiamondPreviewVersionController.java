package com.oto.front.config.controller;

import com.oto.common.core.domain.R;
import com.oto.common.core.validate.AddGroup;
import com.oto.common.core.validate.EditGroup;
import com.oto.common.domain.bo.OtoDiamondPreviewVersionBo;
import com.oto.common.domain.request.CreateVersionRequest;
import com.oto.common.domain.vo.OtoDiamondPreviewVersionVo;
import com.oto.common.excel.utils.ExcelUtil;
import com.oto.common.http.annotation.RemoteServicePort;
import com.oto.common.log.annotation.Log;
import com.oto.common.log.enums.BusinessType;
import com.oto.common.mybatis.core.page.PageQuery;
import com.oto.common.mybatis.core.page.TableDataInfo;
import com.oto.common.web.core.BaseController;
import com.oto.front.config.service.IOtoDiamondPreviewVersionService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 金刚位预览版本管理
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/api/otoconfig/diamond-preview")
@RemoteServicePort
public class RemoteOtoDiamondPreviewVersionController extends BaseController {

    private final IOtoDiamondPreviewVersionService otoDiamondPreviewVersionService;

    /**
     * 分页查询预览版本列表
     */
    @GetMapping("/versions")
    public R<TableDataInfo<OtoDiamondPreviewVersionVo>> page(OtoDiamondPreviewVersionBo bo, PageQuery pageQuery) {
        return R.ok(otoDiamondPreviewVersionService.queryPageList(bo, pageQuery));
    }

    /**
     * 导出预览版本列表
     */
    @Log(title = "金刚位预览版本", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OtoDiamondPreviewVersionBo bo, HttpServletResponse response) {
        List<OtoDiamondPreviewVersionVo> list = otoDiamondPreviewVersionService.queryList(bo);
        ExcelUtil.exportExcel(list, "金刚位预览版本", OtoDiamondPreviewVersionVo.class, response);
    }

    /**
     * 获取当前发布版本
     */
    @GetMapping("/current")
    public R<OtoDiamondPreviewVersionVo> getCurrentVersion() {
        return R.ok(otoDiamondPreviewVersionService.queryCurrentVersion());
    }

    /**
     * 根据版本编码获取预览版本详情
     */
    @GetMapping("/{versionCode}")
    public R<OtoDiamondPreviewVersionVo> getByVersionCode(@PathVariable String versionCode) {
        return R.ok(otoDiamondPreviewVersionService.queryByVersionCode(versionCode));
    }

    /**
     * 根据ID获取预览版本详情
     */
    @GetMapping("/detail/{id}")
    public R<OtoDiamondPreviewVersionVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(otoDiamondPreviewVersionService.queryById(id));
    }

    /**
     * 新增预览版本
     */
    @Log(title = "金刚位预览版本", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OtoDiamondPreviewVersionBo bo) {
        return toAjax(otoDiamondPreviewVersionService.insertByBo(bo));
    }

    /**
     * 创建预览版本（基于当前配置）
     */
    @Log(title = "创建预览版本", businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public R<String> createPreviewVersion(@RequestBody CreateVersionRequest request) {
        String versionCode = otoDiamondPreviewVersionService.createPreviewVersion(
            request.getVersionName(),
            request.getDescription()
        );
        return R.ok(versionCode);
    }

    /**
     * 修改预览版本
     */
    @Log(title = "金刚位预览版本", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OtoDiamondPreviewVersionBo bo) {
        return toAjax(otoDiamondPreviewVersionService.updateByBo(bo));
    }

    /**
     * 更新版本状态
     */
    @Log(title = "更新版本状态", businessType = BusinessType.UPDATE)
    @PutMapping("/{versionCode}/status")
    public R<Void> updateStatus(@PathVariable String versionCode, @RequestBody Map<String, Integer> request) {
        Integer status = request.get("status");
        return toAjax(otoDiamondPreviewVersionService.updateVersionStatus(versionCode, status));
    }

    /**
     * 发布版本
     */
    @Log(title = "发布预览版本", businessType = BusinessType.UPDATE)
    @PostMapping("/{versionCode}/publish")
    public R<Void> publishVersion(@PathVariable String versionCode) {
        return toAjax(otoDiamondPreviewVersionService.publishVersion(versionCode));
    }

    /**
     * 回滚版本
     */
    @Log(title = "回滚预览版本", businessType = BusinessType.UPDATE)
    @PostMapping("/{versionCode}/rollback")
    public R<Void> rollbackVersion(@PathVariable String versionCode) {
        return toAjax(otoDiamondPreviewVersionService.rollbackToVersion(versionCode));
    }

    /**
     * 生成预览URL
     */
    @GetMapping("/{versionCode}/preview-url")
    public R<String> generatePreviewUrl(@PathVariable String versionCode) {
        return R.ok(otoDiamondPreviewVersionService.generatePreviewUrl(versionCode));
    }

    /**
     * 删除预览版本
     */
    @Log(title = "金刚位预览版本", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(otoDiamondPreviewVersionService.deleteWithValidByIds(Arrays.asList(ids), true));
    }



}
