package com.oto.front.config.service;

import com.oto.common.domain.bo.OtoDiamondPreviewVersionBo;
import com.oto.common.domain.vo.OtoDiamondPreviewVersionVo;
import com.oto.common.mybatis.core.page.PageQuery;
import com.oto.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 金刚位预览版本管理Service接口
 *
 * <AUTHOR>
 */
public interface IOtoDiamondPreviewVersionService {

    /**
     * 查询预览版本
     *
     * @param id 预览版本主键
     * @return 预览版本
     */
    OtoDiamondPreviewVersionVo queryById(Long id);

    /**
     * 根据版本编码查询预览版本
     *
     * @param versionCode 版本编码
     * @return 预览版本
     */
    OtoDiamondPreviewVersionVo queryByVersionCode(String versionCode);

    /**
     * 获取当前发布版本
     *
     * @return 当前版本
     */
    OtoDiamondPreviewVersionVo queryCurrentVersion();

    /**
     * 查询预览版本列表
     *
     * @param bo 预览版本
     * @param pageQuery 分页查询
     * @return 预览版本集合
     */
    TableDataInfo<OtoDiamondPreviewVersionVo> queryPageList(OtoDiamondPreviewVersionBo bo, PageQuery pageQuery);

    /**
     * 查询预览版本列表
     *
     * @param bo 预览版本
     * @return 预览版本集合
     */
    List<OtoDiamondPreviewVersionVo> queryList(OtoDiamondPreviewVersionBo bo);

    /**
     * 新增预览版本
     *
     * @param bo 预览版本
     * @return 结果
     */
    Boolean insertByBo(OtoDiamondPreviewVersionBo bo);

    /**
     * 修改预览版本
     *
     * @param bo 预览版本
     * @return 结果
     */
    Boolean updateByBo(OtoDiamondPreviewVersionBo bo);

    /**
     * 校验并批量删除预览版本信息
     *
     * @param ids 主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 创建预览版本（基于当前配置）
     *
     * @param versionName 版本名称
     * @param description 版本描述
     * @return 版本编码
     */
    String createPreviewVersion(String versionName, String description);

    /**
     * 发布预览版本
     *
     * @param versionCode 版本编码
     * @return 结果
     */
    Boolean publishVersion(String versionCode);

    /**
     * 回滚到指定版本
     *
     * @param versionCode 版本编码
     * @return 结果
     */
    Boolean rollbackToVersion(String versionCode);

    /**
     * 更新版本状态
     *
     * @param versionCode 版本编码
     * @param status 状态
     * @return 结果
     */
    Boolean updateVersionStatus(String versionCode, Integer status);

    /**
     * 生成预览URL
     *
     * @param versionCode 版本编码
     * @return 预览URL
     */
    String generatePreviewUrl(String versionCode);

}
