package com.oto.front.config.mapper;

import com.oto.common.domain.OtoDiamondPosition;
import com.oto.common.domain.model.PositionUpdateItem;
import com.oto.common.domain.vo.OtoDiamondPositionVo;
import com.oto.common.mybatis.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 金刚位配置Mapper接口
 *
 * <AUTHOR>
 */
public interface OtoDiamondPositionMapper extends BaseMapperPlus<OtoDiamondPosition, OtoDiamondPositionVo> {

    /**
     * 查询所有启用的金刚位（按排序顺序）
     *
     * @return 启用的金刚位列表
     */
    List<OtoDiamondPositionVo> selectEnabledList();

    /**
     * 获取最大排序序号
     *
     * @return 最大排序序号
     */
    Integer selectMaxSortOrder();

    /**
     * 批量更新状态
     *
     * @param ids 金刚位ID列表
     * @param status 状态值
     * @return 更新行数
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") Integer status);

    /**
     * 批量更新排序
     *
     * @param items 排序项列表
     * @return 更新行数
     */
    int batchUpdateSortOrder(@Param("items") List<OtoDiamondPosition> items);

    // =====================================================
    // 网格布局相关方法
    // =====================================================

    /**
     * 批量更新网格位置
     *
     * @param positions 位置更新列表
     * @return 更新行数
     */
    int batchUpdateGridPositions(@Param("positions") List<PositionUpdateItem> positions);

    /**
     * 检查网格位置冲突
     *
     * @param positions 位置列表
     * @return 冲突信息列表
     */
    List<Map<String, Object>> selectPositionConflicts(@Param("positions") List<PositionUpdateItem> positions);

    /**
     * 查询指定网格位置的金刚位
     *
     * @param gridRow 网格行
     * @param gridCol 网格列
     * @return 金刚位信息
     */
    OtoDiamondPositionVo selectByGridPosition(@Param("gridRow") Integer gridRow, @Param("gridCol") Integer gridCol);

}
