package com.oto.front.config.controller;

import com.oto.common.core.domain.R;
import com.oto.common.core.validate.AddGroup;
import com.oto.common.core.validate.EditGroup;
import com.oto.common.domain.bo.OtoConfigBo;
import com.oto.common.domain.vo.OtoConfigVo;
import com.oto.common.http.annotation.RemoteServicePort;
import com.oto.common.log.annotation.Log;
import com.oto.common.log.enums.BusinessType;
import com.oto.common.mybatis.core.page.PageQuery;
import com.oto.common.mybatis.core.page.TableDataInfo;
import com.oto.common.web.core.BaseController;
import com.oto.front.config.service.OtoConfigService;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * oto参数配置远程服务接口
 * 提供给oto-front-config-admin模块通过OpenFeign调用的接口
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/api/otoconfig/otoconfig")
@RemoteServicePort
public class RemoteOtoConfigController extends BaseController {

    private final OtoConfigService otoConfigService;

    /**
     * 查询oto参数配置列表
     */
    @GetMapping("/list")
    public TableDataInfo<OtoConfigVo> list(OtoConfigBo bo, PageQuery pageQuery) {
        return otoConfigService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出oto参数配置列表
     */
//    @Log(title = "oto参数配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public R<List<OtoConfigVo>> export(@RequestBody OtoConfigBo bo) {
        List<OtoConfigVo> list = otoConfigService.queryList(bo);
        return R.ok(list);
    }

    /**
     * 获取oto参数配置详细信息
     *
     * @param configId 主键
     */
    @GetMapping("/{configId}")
    public R<OtoConfigVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long configId) {
        return R.ok(otoConfigService.queryById(configId));
    }

    /**
     * 新增oto参数配置
     */
//    @Log(title = "oto参数配置", businessType = BusinessType.INSERT)
    @PostMapping()
    public R<String> add(@Validated(AddGroup.class) @RequestBody OtoConfigBo bo) {
        String result = otoConfigService.insertByBo(bo);
        return R.ok(result);
    }

    /**
     * 修改oto参数配置
     */
//    @Log(title = "oto参数配置", businessType = BusinessType.UPDATE)
    @PutMapping()
    public R<String> edit(@Validated(EditGroup.class) @RequestBody OtoConfigBo bo) {
        String result = otoConfigService.updateByBo(bo);
        return R.ok(result);
    }

    /**
     * 删除oto参数配置
     *
     * @param configIds 主键串
     */
//    @Log(title = "oto参数配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{configIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] configIds) {
        Boolean result = otoConfigService.deleteWithValidByIds(List.of(configIds), true);
        return result ? R.ok() : R.fail("删除失败");
    }

    /**
     * 刷新所有配置缓存
     */
//    @Log(title = "oto参数配置", businessType = BusinessType.OTHER)
    @DeleteMapping("/refresh")
    public R<Void> refreshCache() {
        boolean success = otoConfigService.refreshOtoAllConfigCache();
        return success ? R.ok() : R.fail("刷新缓存失败");
    }

    /**
     * 根据配置键获取配置值
     *
     * @param configKey 配置键
     */
    @GetMapping("/value/{configKey}")
    public R<String> getConfigValueByKey(@PathVariable String configKey) {
        String value = otoConfigService.getConfigValueByKey(configKey);
        return R.ok(value);
    }

    /**
     * 根据配置键获取开关配置值
     *
     * @param configKey 配置键
     */
    @GetMapping("/enable/{configKey}")
    public R<Boolean> getEnableByConfigKey(@PathVariable String configKey) {
        String value = otoConfigService.getConfigValueByKey(configKey);
        Boolean enable = "true".equalsIgnoreCase(value) || "Y".equalsIgnoreCase(value) || "1".equals(value);
        return R.ok(enable);
    }
}
