package com.oto.front.config.controller;

import com.oto.common.core.domain.R;
import com.oto.common.core.validate.AddGroup;
import com.oto.common.core.validate.EditGroup;
import com.oto.common.domain.bo.OtoDiamondPositionBo;
import com.oto.common.domain.vo.OtoDiamondPositionVo;


import com.oto.common.log.annotation.Log;
import com.oto.common.log.enums.BusinessType;
import com.oto.common.mybatis.core.page.PageQuery;
import com.oto.common.mybatis.core.page.TableDataInfo;
import com.oto.common.web.core.BaseController;
import com.oto.front.config.service.IOtoDiamondPositionService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.oto.common.http.annotation.RemoteServicePort;


import java.util.List;
import java.util.Map;

/**
 * 金刚位配置管理
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/app/api/otoconfig/diamond-position")
@RemoteServicePort
public class RemoteOtoDiamondPositionController extends BaseController {

    private final IOtoDiamondPositionService otoDiamondPositionService;

    /**
     * 分页查询金刚位列表
     */
    @GetMapping("/page")
    public R<TableDataInfo<OtoDiamondPositionVo>> page(
            OtoDiamondPositionBo bo,
            PageQuery pageQuery) {

        // 确保分页参数有默认值
        if (pageQuery.getPageNum() == null) {
            pageQuery.setPageNum(1);
        }
        if (pageQuery.getPageSize() == null) {
            pageQuery.setPageSize(10);
        }

        return R.ok(otoDiamondPositionService.queryPageList(bo, pageQuery));
    }

    /**
     * 获取所有启用的金刚位（用于前端展示）
     */
    @GetMapping("/list")
    public R<List<OtoDiamondPositionVo>> list() {
        return R.ok(otoDiamondPositionService.queryEnabledList());
    }



    /**
     * 根据ID获取金刚位详情
     */
    @GetMapping("/{id}")
    public R<OtoDiamondPositionVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(otoDiamondPositionService.queryById(id));
    }

    /**
     * 新增金刚位
     */
    @Log(title = "金刚位配置", businessType = BusinessType.INSERT)

    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OtoDiamondPositionBo bo) {
        return toAjax(otoDiamondPositionService.insertByBo(bo));
    }

    /**
     * 更新金刚位
     */
    @Log(title = "金刚位配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OtoDiamondPositionBo bo) {
        return toAjax(otoDiamondPositionService.updateByBo(bo));
    }

    /**
     * 删除金刚位
     */
    @Log(title = "金刚位配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<Void> remove(@NotNull(message = "主键不能为空")
                          @PathVariable Long id) {
        return toAjax(otoDiamondPositionService.deleteWithValidByIds(List.of(id), true));
    }



    /**
     * 更新金刚位排序
     */
    @Log(title = "金刚位配置", businessType = BusinessType.UPDATE)
    @PutMapping("/sort")
    public R<Void> updateSort(@RequestBody List<OtoDiamondPositionBo> sortList) {
        return toAjax(otoDiamondPositionService.updateSortOrder(sortList));
    }

    /**
     * 启用/禁用金刚位
     */
    @Log(title = "金刚位配置", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/status")
    public R<Void> updateStatus(@PathVariable Long id,
                                @RequestParam Integer status) {
        return toAjax(otoDiamondPositionService.batchUpdateStatus(List.of(id), status));
    }

    /**
     * 批量更新状态
     */
    @Log(title = "金刚位配置", businessType = BusinessType.UPDATE)
    @PatchMapping("/batch/status")
    public R<Void> batchUpdateStatus(@RequestBody Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        List<Long> ids = (List<Long>) request.get("ids");
        Integer status = (Integer) request.get("status");
        return toAjax(otoDiamondPositionService.batchUpdateStatus(ids, status));
    }



}
