package com.oto.front.config.controller;

import com.oto.common.core.domain.R;
import com.oto.common.domain.model.GridLayoutData;
import com.oto.common.domain.request.AutoAssignRequest;
import com.oto.common.domain.request.PositionUpdateRequest;
import com.oto.common.domain.vo.OtoDiamondPositionVo;

import com.oto.common.http.annotation.RemoteServicePort;
import com.oto.common.log.annotation.Log;
import com.oto.common.log.enums.BusinessType;
import com.oto.common.web.core.BaseController;
import com.oto.front.config.service.DiamondConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 金刚位布局管理控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/app/api/otoconfig/diamond-layout")
@RequiredArgsConstructor
@Validated
@RemoteServicePort

public class RemoteDiamondLayoutController extends BaseController {

    private final DiamondConfigService diamondConfigService;

    /**
     * 获取完整的网格布局数据
     */
    @GetMapping("/complete")
    public R<GridLayoutData> getCompleteLayout() {
        return R.ok(diamondConfigService.getCompleteGridLayout());
    }

    /**
     * 获取未使用的金刚位列表
     */
    @GetMapping("/unused")
    public R<List<OtoDiamondPositionVo>> getUnusedPositions() {
        return R.ok(diamondConfigService.getUnusedPositions());
    }

    /**
     * 批量更新金刚位位置
     */
    @Log(title = "金刚位位置更新", businessType = BusinessType.UPDATE)
    @PutMapping("/positions")
    public R<Void> updatePositions(@RequestBody PositionUpdateRequest request) {
        return toAjax(diamondConfigService.updatePositionsLayout(request.getPositions()));
    }



    /**
     * 自动分配位置
     */
    @Log(title = "金刚位自动分配", businessType = BusinessType.UPDATE)
    @PostMapping("/auto-assign")
    public R<Void> autoAssignPositions(@RequestBody AutoAssignRequest request) {
        return toAjax(diamondConfigService.autoAssignPositions(request.getPositionIds()));
    }







}
