package com.oto.front.config.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.oto.common.core.utils.StringUtils;
import com.oto.common.domain.OtoDiamondPreviewVersion;
import com.oto.common.domain.bo.OtoDiamondPreviewVersionBo;
import com.oto.common.domain.vo.OtoDiamondGridConfigVo;
import com.oto.common.domain.vo.OtoDiamondPositionVo;
import com.oto.common.domain.vo.OtoDiamondPreviewVersionVo;
import com.oto.common.mybatis.core.page.PageQuery;
import com.oto.common.mybatis.core.page.TableDataInfo;
import com.oto.front.config.mapper.OtoDiamondPreviewVersionMapper;
import com.oto.front.config.service.DiamondConfigService;
import com.oto.front.config.service.IOtoDiamondPositionService;
import com.oto.front.config.service.IOtoDiamondPreviewVersionService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.List;

/**
 * 金刚位预览版本管理Service业务层处理
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class OtoDiamondPreviewVersionServiceImpl implements IOtoDiamondPreviewVersionService {

    private final OtoDiamondPreviewVersionMapper baseMapper;
    private final DiamondConfigService gridConfigService;
    private final IOtoDiamondPositionService diamondPositionService;

    /**
     * 查询预览版本
     */
    @Override
    public OtoDiamondPreviewVersionVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 根据版本编码查询预览版本
     */
    @Override
    public OtoDiamondPreviewVersionVo queryByVersionCode(String versionCode) {
        return baseMapper.selectByVersionCode(versionCode);
    }

    /**
     * 获取当前发布版本
     */
    @Override
    public OtoDiamondPreviewVersionVo queryCurrentVersion() {
        return baseMapper.selectCurrentVersion();
    }

    /**
     * 查询预览版本列表
     */
    @Override
    public TableDataInfo<OtoDiamondPreviewVersionVo> queryPageList(OtoDiamondPreviewVersionBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OtoDiamondPreviewVersion> lqw = buildQueryWrapper(bo);
        Page<OtoDiamondPreviewVersionVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询预览版本列表
     */
    @Override
    public List<OtoDiamondPreviewVersionVo> queryList(OtoDiamondPreviewVersionBo bo) {
        LambdaQueryWrapper<OtoDiamondPreviewVersion> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OtoDiamondPreviewVersion> buildQueryWrapper(OtoDiamondPreviewVersionBo bo) {
        LambdaQueryWrapper<OtoDiamondPreviewVersion> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getVersionName()), OtoDiamondPreviewVersion::getVersionName, bo.getVersionName());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionCode()), OtoDiamondPreviewVersion::getVersionCode, bo.getVersionCode());
        lqw.eq(ObjectUtil.isNotNull(bo.getStatus()), OtoDiamondPreviewVersion::getStatus, bo.getStatus());
        lqw.eq(ObjectUtil.isNotNull(bo.getIsCurrent()), OtoDiamondPreviewVersion::getIsCurrent, bo.getIsCurrent());
        lqw.orderByDesc(OtoDiamondPreviewVersion::getCreateTime);
        return lqw;
    }

    /**
     * 新增预览版本
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(OtoDiamondPreviewVersionBo bo) {
        OtoDiamondPreviewVersion add = BeanUtil.toBean(bo, OtoDiamondPreviewVersion.class);
        validEntityBeforeSave(add);

        // 生成版本编码
        if (StrUtil.isBlank(add.getVersionCode())) {
            add.setVersionCode(generateVersionCode());
        }

        // 生成预览URL
        if (StrUtil.isBlank(add.getPreviewUrl())) {
            add.setPreviewUrl(generatePreviewUrl(add.getVersionCode()));
        }

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改预览版本
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(OtoDiamondPreviewVersionBo bo) {
        OtoDiamondPreviewVersion update = BeanUtil.toBean(bo, OtoDiamondPreviewVersion.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OtoDiamondPreviewVersion entity) {
        // 检查版本编码是否重复
        if (StrUtil.isNotBlank(entity.getVersionCode())) {
            LambdaQueryWrapper<OtoDiamondPreviewVersion> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(OtoDiamondPreviewVersion::getVersionCode, entity.getVersionCode());
            if (entity.getId() != null) {
                wrapper.ne(OtoDiamondPreviewVersion::getId, entity.getId());
            }
            long count = baseMapper.selectCount(wrapper);
            if (count > 0) {
                throw new RuntimeException("版本编码已存在");
            }
        }
    }

    /**
     * 批量删除预览版本
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 检查是否有已发布的版本
            LambdaQueryWrapper<OtoDiamondPreviewVersion> wrapper = Wrappers.lambdaQuery();
            wrapper.in(OtoDiamondPreviewVersion::getId, ids)
                   .eq(OtoDiamondPreviewVersion::getStatus, 2); // 已发布状态
            long publishedCount = baseMapper.selectCount(wrapper);
            if (publishedCount > 0) {
                throw new RuntimeException("不能删除已发布的版本");
            }
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 创建预览版本（基于当前配置）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createPreviewVersion(String versionName, String description) {
        // 获取当前网格配置
        OtoDiamondGridConfigVo gridConfig = gridConfigService.getActiveGridConfig();

        // 获取当前金刚位配置
        List<OtoDiamondPositionVo> positions = diamondPositionService.queryEnabledList();

        // 构建版本数据
        OtoDiamondPreviewVersion version = new OtoDiamondPreviewVersion();
        version.setVersionName(versionName);
        version.setVersionCode(generateVersionCode());
        version.setGridConfig(JSONUtil.toJsonStr(gridConfig));
        version.setPositionsConfig(JSONUtil.toJsonStr(positions));
        version.setPreviewUrl(generatePreviewUrl(version.getVersionCode()));
        version.setStatus(0); // 草稿状态
        version.setIsCurrent(0);

        baseMapper.insert(version);
        return version.getVersionCode();
    }

    /**
     * 发布预览版本
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean publishVersion(String versionCode) {
        // 检查版本是否存在
        OtoDiamondPreviewVersionVo version = queryByVersionCode(versionCode);
        if (version == null) {
            throw new RuntimeException("版本不存在");
        }

        // 检查版本状态
        if (version.getStatus() != 1) {
            throw new RuntimeException("只能发布预览中的版本");
        }

        LocalDateTime now = LocalDateTime.now();

        // 1. 先更新当前发布版本的停止时间
        baseMapper.updateStopTimeForCurrentVersion(now);

        // 2. 清除当前版本标记
        baseMapper.clearCurrentStatus();

        // 3. 更新新版本状态为已发布，并设为当前版本
        OtoDiamondPreviewVersion update = new OtoDiamondPreviewVersion();
        update.setId(version.getId());
        update.setStatus(2); // 已发布
        update.setIsCurrent(1); // 当前版本
        update.setPublishTime(now); // 设置发布时间
        update.setStopTime(null); // 清空停止时间（当前版本不应该有停止时间）

        return baseMapper.updateById(update) > 0;
    }

    /**
     * 回滚到指定版本
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean rollbackToVersion(String versionCode) {
        // 检查版本是否存在
        OtoDiamondPreviewVersionVo version = queryByVersionCode(versionCode);
        if (version == null) {
            throw new RuntimeException("版本不存在");
        }

        // 检查版本状态
        if (version.getStatus() != 2) {
            throw new RuntimeException("只能回滚到已发布的版本");
        }

        // 清除当前版本标记
        baseMapper.clearCurrentStatus();

        // 设置为当前版本
        baseMapper.setCurrentVersion(versionCode);

        return true;
    }

    /**
     * 更新版本状态
     */
    @Override
    public Boolean updateVersionStatus(String versionCode, Integer status) {
        return baseMapper.updateVersionStatus(versionCode, status) > 0;
    }

    /**
     * 生成预览URL
     */
    @Override
    public String generatePreviewUrl(String versionCode) {
        return "/preview/" + versionCode;
    }

    /**
     * 生成版本编码
     */
    private String generateVersionCode() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        return "v" + timestamp;
    }

}
