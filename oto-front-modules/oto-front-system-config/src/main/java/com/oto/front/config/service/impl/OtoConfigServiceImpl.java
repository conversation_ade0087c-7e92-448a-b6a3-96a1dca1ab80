package com.oto.front.config.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.oto.common.domain.OtoConfig;
import com.oto.common.domain.bo.OtoConfigBo;
import com.oto.common.domain.vo.OtoConfigVo;
import com.oto.constantandenums.enums.OtoConfigKeyEnums;
import com.oto.common.core.utils.MapstructUtils;
import com.oto.common.core.utils.StringUtils;
import com.oto.common.mybatis.core.page.TableDataInfo;
import com.oto.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.oto.front.config.mapper.OtoConfigMapper;
import com.oto.front.config.service.OtoConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Service;


import com.oto.common.core.constant.CacheNames;
import com.oto.common.core.exception.ServiceException;
import com.oto.common.core.constant.SystemConstants;

import java.util.List;
import java.util.Map;
import java.util.Collection;

import org.springframework.transaction.annotation.Transactional;
import com.oto.common.redis.utils.CacheUtils;

/**
 * oto参数配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DS("master")
public class OtoConfigServiceImpl implements OtoConfigService {

    private final OtoConfigMapper baseMapper;

    /**
     * 查询oto参数配置
     *
     * @param configId 主键
     * @return oto参数配置
     */
    @Override
    public OtoConfigVo queryById(Long configId){
        return baseMapper.selectVoById(configId);
    }

    /**
     * 分页查询oto参数配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return oto参数配置分页列表
     */
    @Override
    public TableDataInfo<OtoConfigVo> queryPageList(OtoConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OtoConfig> lqw = buildQueryWrapper(bo);
        Page<OtoConfigVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的oto参数配置列表
     *
     * @param bo 查询条件
     * @return oto参数配置列表
     */
    @Override
    public List<OtoConfigVo> queryList(OtoConfigBo bo) {
        LambdaQueryWrapper<OtoConfig> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OtoConfig> buildQueryWrapper(OtoConfigBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OtoConfig> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(OtoConfig::getConfigId);
        lqw.like(StringUtils.isNotBlank(bo.getConfigName()), OtoConfig::getConfigName, bo.getConfigName());
        lqw.eq(StringUtils.isNotBlank(bo.getConfigKey()), OtoConfig::getConfigKey, bo.getConfigKey());
        lqw.eq(StringUtils.isNotBlank(bo.getConfigValue()), OtoConfig::getConfigValue, bo.getConfigValue());
        lqw.eq(StringUtils.isNotBlank(bo.getConfigType()), OtoConfig::getConfigType, bo.getConfigType());
        lqw.eq(StringUtils.isNotBlank(bo.getRemark()), OtoConfig::getRemark, bo.getRemark());
        return lqw;
    }

    /**
     * 新增oto参数配置
     *
     * @param bo oto参数配置
     * @return 是否新增成功
     */
    @Override
    @CachePut(cacheNames = CacheNames.OTO_CONFIG, key = "#bo.configKey")
    @Transactional(rollbackFor = Exception.class)
    public String insertByBo(OtoConfigBo bo) {
        LambdaQueryWrapper<OtoConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(OtoConfig::getConfigKey, bo.getConfigKey());
        if (baseMapper.selectCount(lqw) > 0) {
            throw new ServiceException("参数键名已存在，请勿重复添加！");
        }
        OtoConfig add = MapstructUtils.convert(bo, OtoConfig.class);
        boolean flag = baseMapper.insert(add) > 0;
        if (!flag) {
            throw new ServiceException("新增失败，请重试！");
        }
        bo.setConfigId(add.getConfigId());
        return add.getConfigValue();
    }

    /**
     * 修改oto参数配置
     *
     * @param bo oto参数配置
     * @return 是否修改成功
     */
    @Override
    @CachePut(cacheNames = CacheNames.OTO_CONFIG, key = "#bo.configKey")
    @Transactional(rollbackFor = Exception.class)
    public String updateByBo(OtoConfigBo bo) {
        OtoConfig update = MapstructUtils.convert(bo, OtoConfig.class);
        boolean flag = baseMapper.updateById(update) > 0;
        if (!flag) {
            throw new ServiceException("修改失败，请重试！");
        }
        return update.getConfigValue();
    }

    /**
     * 校验并批量删除oto参数配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        // 校验是否包含系统内置参数
        LambdaQueryWrapper<OtoConfig> lqw = Wrappers.lambdaQuery();
        lqw.in(OtoConfig::getConfigId, ids);
        lqw.eq(OtoConfig::getConfigType, SystemConstants.YES);
        if (baseMapper.selectCount(lqw) > 0) {
            throw new ServiceException("删除选项包含系统内置参数不允许删除！");
        }
        // 查询所有待删参数的configKey
        LambdaQueryWrapper<OtoConfig> keyQuery = Wrappers.lambdaQuery();
        keyQuery.in(OtoConfig::getConfigId, ids);
        List<OtoConfig> configs = baseMapper.selectList(keyQuery);
        boolean flag = baseMapper.deleteByIds(ids) > 0;

        if (flag && isValid) {
            // 清除缓存
            for (OtoConfig config : configs) {
                CacheUtils.evict(CacheNames.OTO_CONFIG, config.getConfigKey());
            }
        }
        return flag;
    }


    private Boolean getEnableByConfigKey(String configKey) {
        if (StringUtils.isBlank(configKey)) {
            return true; // 默认开启
        }

        OtoConfig config = baseMapper.selectOne(Wrappers.<OtoConfig>lambdaQuery()
            .eq(OtoConfig::getConfigKey, configKey)
            .last("limit 1"));
        if (config != null) {
            String configValue = config.getConfigValue();
            return "true".equalsIgnoreCase(configValue);
        }
        // 如果没有找到配置，返回默认值true  ，并通知运维人员
        log.warn("配置键 {} 未找到，返回默认值 true", configKey);
        return true;
    }

    /**
     * 根据配置键获取配置值 开关  默认值都是开启
     * 这个方法只适用于获取开关配置的值
     *
     * @param configKey 配置键
     * @return 配置值
     */
    @Override
    public Boolean getEnableByConfigKey(OtoConfigKeyEnums configKey) {
        String key = configKey.getKey();
        if (StringUtils.isBlank(key)) {
            return true; // 默认开启
        }
        // 先从缓存获取
        Object cacheValue = CacheUtils.get(CacheNames.OTO_CONFIG, key);
        if (cacheValue != null) {
            if (cacheValue instanceof Boolean) {
                return (Boolean) cacheValue;
            }
            return Boolean.TRUE.toString().equalsIgnoreCase(String.valueOf(cacheValue));
        }

        // 如果缓存中没有，则调用原方法获取
        Boolean result = getEnableByConfigKey(key);
        // 存入缓存
        CacheUtils.put(CacheNames.OTO_CONFIG, key, result);
        return result;
    }

    /**
     * 获取oto配置值（带缓存）
     * 支持空值缓存，防止缓存穿透
     *
     * @param configKey 配置键
     * @return 配置值
     */
    @Override
    @Cacheable(cacheNames = CacheNames.OTO_CONFIG, key = "#configKey", unless = "#result == null")
    public String getConfigValueByKey(String configKey) {
        if (StringUtils.isBlank(configKey)) {
            return null;
        }
        LambdaQueryWrapper<OtoConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(OtoConfig::getConfigKey, configKey);
        lqw.last("limit 1");
        OtoConfig config = baseMapper.selectOne(lqw);
        return config != null ? config.getConfigValue() : null;
    }

    /**
     * 刷新所有oto参数配置缓存
     * 使用 CacheManager 清除模块内所有缓存
     *
     * @return 是否刷新成功
     */
    @Override
    public Boolean refreshOtoAllConfigCache() {
        try {
            CacheUtils.clear(CacheNames.OTO_CONFIG);
            return true;
        } catch (Exception e) {
            log.error("刷新oto参数配置缓存失败", e);
            return false;
        }
    }
}
