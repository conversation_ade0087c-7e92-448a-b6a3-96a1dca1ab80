package com.oto.front.config.service;

import com.oto.common.core.exception.ServiceException;
import com.oto.common.domain.bo.OtoConfigBo;
import com.oto.common.domain.bo.OtoDiamondPositionBo;
import com.oto.common.domain.model.ConflictInfo;
import com.oto.common.domain.model.DiamondGridConfigModel;
import com.oto.common.domain.model.GridLayoutData;
import com.oto.common.domain.model.PositionUpdateItem;
import com.oto.common.domain.vo.OtoConfigVo;
import com.oto.common.domain.vo.OtoDiamondGridConfigVo;
import com.oto.common.domain.vo.OtoDiamondPositionVo;
import com.oto.common.json.utils.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 金刚位配置服务
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DiamondConfigService {

    // 配置键常量
    private static final String DIAMOND_GRID_CONFIG_KEY = "oto.diamond.grid.config";

    private final IOtoDiamondPositionService diamondPositionService;
    private final OtoConfigService otoConfigService;

    /**
     * 获取网格配置（唯一配置）
     */
    public OtoDiamondGridConfigVo getGridConfig() {
        try {
            // 从系统配置表获取配置
            String configValue = otoConfigService.getConfigValueByKey(DIAMOND_GRID_CONFIG_KEY);

            if (configValue == null || configValue.trim().isEmpty()) {
                return getDefaultGridConfig();
            }

            // 解析JSON配置
            DiamondGridConfigModel model = JsonUtils.parseObject(configValue, DiamondGridConfigModel.class);
            if (model == null) {
                return getDefaultGridConfig();
            }

            return convertToVo(model);
        } catch (Exception e) {
            log.error("获取网格配置失败，返回默认配置", e);
            return getDefaultGridConfig();
        }
    }

    /**
     * 获取当前启用的网格配置（兼容原有接口）
     */
    public OtoDiamondGridConfigVo getActiveGridConfig() {
        return getGridConfig();
    }

    /**
     * 保存网格配置到系统配置表
     */
    @Transactional
    public Boolean saveGridConfig(OtoDiamondGridConfigVo config) {
        try {
            // 转换为数据模型
            DiamondGridConfigModel model = convertToModel(config);

            // 保存到系统配置表
            String configValue = JsonUtils.toJsonString(model);

            // 查找现有配置并更新
            return updateConfigValue(DIAMOND_GRID_CONFIG_KEY, configValue);
        } catch (Exception e) {
            log.error("保存网格配置失败", e);
            return false;
        }
    }

    /**
     * 新增行配置
     * @param rowConfig 行配置信息
     * @return 新增结果
     */
    @Transactional
    public Boolean addGridRow(OtoDiamondGridConfigVo.RowConfig rowConfig) {
        log.info("开始新增行配置: {}", rowConfig);

        // 1. 获取当前网格配置
        OtoDiamondGridConfigVo currentConfig = getActiveGridConfig();
        if (currentConfig == null) {
            log.error("获取当前网格配置失败");
            throw new ServiceException("获取当前网格配置失败");
        }

        List<OtoDiamondGridConfigVo.RowConfig> currentRowConfigs = currentConfig.getRowConfigs();
        if (currentRowConfigs == null) {
            currentRowConfigs = new ArrayList<>();
        }

        // 2. 设置新行的索引和排序（自动计算）
        int newRowIndex = currentRowConfigs.size() + 1;
        rowConfig.setRowIndex(newRowIndex);
        rowConfig.setSortOrder(newRowIndex);

        // 3. 添加新行配置
        currentRowConfigs.add(rowConfig);

        // 4. 更新配置（总行数自动计算）
        currentConfig.setRowConfigs(currentRowConfigs);
        currentConfig.setTotalRows(currentRowConfigs.size());

        log.info("新增后行配置数量: {}", currentRowConfigs.size());

        // 5. 保存更新后的配置
        Boolean result = saveGridConfig(currentConfig);

        if (!result) {
            throw new ServiceException("保存配置失败");
        }

        log.info("新增行配置成功，新行索引: {}, 总行数: {}", newRowIndex, currentRowConfigs.size());
        return result;
    }

    /**
     * 更新行配置
     * @param rowId 要更新的行ID
     * @param rowConfig 新的行配置信息
     * @return 更新结果
     */
    @Transactional
    public Boolean updateGridRow(Integer rowId, OtoDiamondGridConfigVo.RowConfig rowConfig) {
        log.info("开始更新行配置，rowId: {}, newConfig: {}", rowId, rowConfig);

        // 1. 获取当前网格配置
        OtoDiamondGridConfigVo currentConfig = getActiveGridConfig();
        if (currentConfig == null || currentConfig.getRowConfigs() == null) {
            log.error("获取当前网格配置失败或配置为空");
            throw new ServiceException("获取当前网格配置失败");
        }

        List<OtoDiamondGridConfigVo.RowConfig> currentRowConfigs = currentConfig.getRowConfigs();

        // 2. 查找要更新的行
        OtoDiamondGridConfigVo.RowConfig targetRow = null;
        for (OtoDiamondGridConfigVo.RowConfig row : currentRowConfigs) {
            if (row.getRowIndex().equals(rowId) || row.getSortOrder().equals(rowId)) {
                targetRow = row;
                break;
            }
        }

        if (targetRow == null) {
            log.error("要更新的行不存在，rowId: {}", rowId);
            throw new ServiceException("要更新的行不存在");
        }

        // 3. 更新行配置（保持原有的rowIndex和sortOrder）
        targetRow.setColumnCount(rowConfig.getColumnCount());
        targetRow.setRowHeight(rowConfig.getRowHeight());

        // 4. 总行数保持不变（通过行数据计算）
        currentConfig.setTotalRows(currentRowConfigs.size());

        log.info("更新行配置: rowIndex={}, columnCount={}, rowHeight={}",
            targetRow.getRowIndex(), targetRow.getColumnCount(), targetRow.getRowHeight());

        // 5. 保存更新后的配置
        Boolean result = saveGridConfig(currentConfig);

        if (!result) {
            throw new ServiceException("保存配置失败");
        }

        log.info("更新行配置成功，rowId: {}", rowId);
        return result;
    }

    /**
     * 删除指定行配置
     * @param rowId 要删除的行ID（对应rowIndex或sortOrder）
     * @return 删除结果
     */
    @Transactional
    public Boolean removeGridRow(Integer rowId) {
        try {
            log.info("开始删除行配置，rowId: {}", rowId);

            // 1. 获取当前网格配置
            OtoDiamondGridConfigVo currentConfig = getActiveGridConfig();
            if (currentConfig == null || currentConfig.getRowConfigs() == null) {
                log.error("获取当前网格配置失败或配置为空");
                return false;
            }

            List<OtoDiamondGridConfigVo.RowConfig> currentRowConfigs = currentConfig.getRowConfigs();
            log.info("当前行配置数量: {}", currentRowConfigs.size());

            // 2. 检查是否存在要删除的行
            boolean rowExists = currentRowConfigs.stream()
                .anyMatch(row -> row.getRowIndex().equals(rowId) || row.getSortOrder().equals(rowId));

            if (!rowExists) {
                log.error("要删除的行不存在，rowId: {}", rowId);
                throw new ServiceException("要删除的行不存在");
            }

            // 3. 过滤掉要删除的行
            List<OtoDiamondGridConfigVo.RowConfig> newRowConfigs = currentRowConfigs.stream()
                .filter(row -> !row.getRowIndex().equals(rowId) && !row.getSortOrder().equals(rowId))
                .collect(Collectors.toList());

            // 4. 重新排序剩余行
            for (int i = 0; i < newRowConfigs.size(); i++) {
                OtoDiamondGridConfigVo.RowConfig rowConfig = newRowConfigs.get(i);
                rowConfig.setRowIndex(i + 1);
                rowConfig.setSortOrder(i + 1);
            }

            // 5. 更新配置（总行数自动计算）
            currentConfig.setRowConfigs(newRowConfigs);
            currentConfig.setTotalRows(newRowConfigs.size());

            log.info("删除后行配置数量: {}", newRowConfigs.size());

            // 6. 保存更新后的配置
            Boolean result = saveGridConfig(currentConfig);

            if (result) {
                log.info("删除行配置成功，rowId: {}, 剩余行数: {}", rowId, newRowConfigs.size());
            } else {
                log.error("删除行配置失败，保存配置时出错");
            }

            return result;
        } catch (Exception e) {
            log.error("删除行配置失败，rowId: {}", rowId, e);
            throw new ServiceException("删除行配置失败：" + e.getMessage());
        }
    }

    /**
     * 更新配置值（实际保存到数据库）
     */
    private Boolean updateConfigValue(String configKey, String configValue) {
        try {
            log.info("开始更新配置到数据库: {} = {}", configKey, configValue);

            // 检查配置是否存在
            String existingValue = otoConfigService.getConfigValueByKey(configKey);

            if (existingValue != null) {
                // 配置存在，直接更新数据库中的配置值
                log.info("配置已存在，执行更新操作: {}", configKey);

                // 先查询现有配置获取ID
                OtoConfigBo queryBo = new OtoConfigBo();
                queryBo.setConfigKey(configKey);
                List<OtoConfigVo> existingConfigs = otoConfigService.queryList(queryBo);

                if (existingConfigs.isEmpty()) {
                    log.error("配置查询失败，configKey: {}", configKey);
                    throw new ServiceException("配置查询失败");
                }

                OtoConfigVo existingConfig = existingConfigs.get(0);

                // 创建更新Bo对象，包含ID
                OtoConfigBo configBo = new OtoConfigBo();
                configBo.setConfigId(existingConfig.getConfigId());
                configBo.setConfigKey(configKey);
                configBo.setConfigValue(configValue);

                // 使用updateByBo方法更新配置
                String result = otoConfigService.updateByBo(configBo);
                boolean success = result != null;
                log.info("配置更新结果: {}, 返回值: {}", success, result);
                return success;
            } else {
                // 配置不存在，创建新配置
                log.info("配置不存在，执行创建操作: {}", configKey);

                OtoConfigBo configBo = new OtoConfigBo();
                configBo.setConfigKey(configKey);
                configBo.setConfigValue(configValue);
                configBo.setConfigName("金刚位网格配置");
                configBo.setConfigType("Y");
                configBo.setRemark("金刚位网格布局配置");

                String result = otoConfigService.insertByBo(configBo);
                boolean success = result != null;
                log.info("配置创建结果: {}, 返回值: {}", success, result);
                return success;
            }
        } catch (Exception e) {
            log.error("更新配置失败", e);
            return false;
        }
    }

    /**
     * 获取完整的网格布局数据
     */
    public GridLayoutData getCompleteGridLayout() {
        // 1. 获取网格配置
        OtoDiamondGridConfigVo gridConfig = getGridConfig();

        // 2. 获取所有启用的金刚位
        List<OtoDiamondPositionVo> positions = diamondPositionService.queryEnabledList();

        // 3. 构建位置矩阵
        Map<String, OtoDiamondPositionVo> positionMatrix = buildPositionMatrix(gridConfig, positions);

        // 4. 计算统计信息
        GridLayoutData.GridStatistics statistics = calculateStatistics(gridConfig, positions);

        // 5. 组装返回数据
        GridLayoutData layoutData = new GridLayoutData();
        layoutData.setGridConfig(convertToModel(gridConfig));
        layoutData.setPositions(positions);
        layoutData.setPositionMatrix(positionMatrix);
        layoutData.setStatistics(statistics);

        return layoutData;
    }

    /**
     * 批量更新金刚位位置
     */
    @Transactional
    public Boolean updatePositionsLayout(List<PositionUpdateItem> updates) {
        try {
            // 1. 验证网格边界
            validateGridBoundaries(updates);

            // 2. 验证位置冲突
            List<ConflictInfo> conflicts = checkPositionConflicts(updates);
            if (!conflicts.isEmpty()) {
                throw new ServiceException("存在位置冲突，请检查后重试");
            }

            // 3. 批量更新位置和使用状态
            for (PositionUpdateItem update : updates) {
                OtoDiamondPositionBo bo = new OtoDiamondPositionBo();
                bo.setId(update.getId());
                bo.setGridRow(update.getGridRow());
                bo.setGridCol(update.getGridCol());

                // 设置使用状态
                if (update.getGridRow() != null && update.getGridCol() != null) {
                    bo.setIsUse(1); // 已使用
                } else {
                    bo.setIsUse(0); // 未使用
                }

                diamondPositionService.updateByBo(bo);
            }

            return true;
        } catch (Exception e) {
            log.error("更新位置布局失败", e);
            throw new ServiceException("更新位置布局失败：" + e.getMessage());
        }
    }

    /**
     * 验证网格边界
     * 检查金刚位配置的位置是否超出网格范围
     */
    private void validateGridBoundaries(List<PositionUpdateItem> updates) {
        // 获取当前激活的网格配置
        OtoDiamondGridConfigVo activeConfig = getActiveGridConfig();
        if (activeConfig == null) {
            throw new ServiceException("未找到激活的网格配置");
        }

        int maxRows = activeConfig.getTotalRows();
        // 计算最大列数：遍历所有行配置，找出最大的列数
        int maxCols = activeConfig.getRowConfigs().stream()
            .mapToInt(OtoDiamondGridConfigVo.RowConfig::getColumnCount)
            .max()
            .orElse(4); // 默认4列

        for (PositionUpdateItem update : updates) {
            // 跳过位置为null的项（表示移除）
            if (update.getGridRow() == null || update.getGridCol() == null) {
                continue;
            }

            // 验证行边界
            if (update.getGridRow() < 1 || update.getGridRow() > maxRows) {
                throw new ServiceException(String.format(
                    "金刚位位置超出网格范围：行位置 %d 超出范围 [1, %d]",
                    update.getGridRow(), maxRows
                ));
            }

            // 验证列边界
            if (update.getGridCol() < 1 || update.getGridCol() > maxCols) {
                throw new ServiceException(String.format(
                    "金刚位位置超出网格范围：列位置 %d 超出范围 [1, %d]",
                    update.getGridCol(), maxCols
                ));
            }
        }
    }

    /**
     * 检测位置冲突
     */
    public List<ConflictInfo> checkPositionConflicts(List<PositionUpdateItem> positions) {
        List<ConflictInfo> conflicts = new ArrayList<>();

        // 检查当前请求中的冲突
        Map<String, List<PositionUpdateItem>> positionMap = positions.stream()
            .filter(item -> item.getGridRow() != null && item.getGridCol() != null)
            .collect(Collectors.groupingBy(item ->
                item.getGridRow() + "," + item.getGridCol()));

        for (Map.Entry<String, List<PositionUpdateItem>> entry : positionMap.entrySet()) {
            if (entry.getValue().size() > 1) {
                ConflictInfo conflict = new ConflictInfo();
                conflict.setPosition(entry.getKey());
                conflict.setConflictIds(entry.getValue().stream()
                    .map(PositionUpdateItem::getId)
                    .collect(Collectors.toList()));
                conflicts.add(conflict);
            }
        }

        return conflicts;
    }

    /**
     * 获取未使用的金刚位列表
     */
    public List<OtoDiamondPositionVo> getUnusedPositions() {
        OtoDiamondPositionBo queryBo = new OtoDiamondPositionBo();
        queryBo.setIsUse(0); // 未使用
        queryBo.setStatus(1); // 启用状态

        return diamondPositionService.queryList(queryBo);
    }

    /**
     * 自动分配位置
     */
    @Transactional
    public Boolean autoAssignPositions(List<Long> positionIds) {
        try {
            // 1. 获取网格配置
            OtoDiamondGridConfigVo gridConfig = getGridConfig();

            // 2. 获取当前已占用的位置
            Set<String> occupiedPositions = getOccupiedPositions();

            // 3. 生成可用位置列表
            List<String> availablePositions = generateAvailablePositions(gridConfig, occupiedPositions);

            // 4. 为每个金刚位分配位置
            List<PositionUpdateItem> updates = new ArrayList<>();
            for (int i = 0; i < positionIds.size() && i < availablePositions.size(); i++) {
                String position = availablePositions.get(i);
                String[] parts = position.split(",");

                PositionUpdateItem update = new PositionUpdateItem();
                update.setId(positionIds.get(i));
                update.setGridRow(Integer.parseInt(parts[0]));
                update.setGridCol(Integer.parseInt(parts[1]));
                updates.add(update);
            }

            // 5. 批量更新位置
            return updatePositionsLayout(updates);
        } catch (Exception e) {
            log.error("自动分配位置失败", e);
            return false;
        }
    }

    /**
     * 获取默认网格配置
     */
    private OtoDiamondGridConfigVo getDefaultGridConfig() {
        OtoDiamondGridConfigVo defaultConfig = new OtoDiamondGridConfigVo();
        defaultConfig.setId(1L);
        defaultConfig.setTotalRows(6);
        defaultConfig.setGridGap(16);
        defaultConfig.setIsActive(1);
        defaultConfig.setDescription("默认网格配置：6行4列，适配所有设备");

        // 设置默认行配置
        List<OtoDiamondGridConfigVo.RowConfig> rowConfigs = new ArrayList<>();
        for (int i = 1; i <= 6; i++) {
            OtoDiamondGridConfigVo.RowConfig rowConfig = new OtoDiamondGridConfigVo.RowConfig();
            rowConfig.setRowIndex(i);
            rowConfig.setColumnCount(4);
            rowConfig.setRowHeight(80);
            rowConfig.setSortOrder(i);
            rowConfigs.add(rowConfig);
        }
        defaultConfig.setRowConfigs(rowConfigs);

        return defaultConfig;
    }

    /**
     * 构建位置矩阵
     */
    private Map<String, OtoDiamondPositionVo> buildPositionMatrix(
        OtoDiamondGridConfigVo gridConfig,
        List<OtoDiamondPositionVo> positions) {

        Map<String, OtoDiamondPositionVo> matrix = new HashMap<>();

        // 初始化所有位置为null
        if (gridConfig.getRowConfigs() != null) {
            for (OtoDiamondGridConfigVo.RowConfig rowConfig : gridConfig.getRowConfigs()) {
                for (int col = 1; col <= rowConfig.getColumnCount(); col++) {
                    String key = rowConfig.getRowIndex() + "," + col;
                    matrix.put(key, null);
                }
            }
        }

        // 填充金刚位数据
        for (OtoDiamondPositionVo position : positions) {
            if (position.getGridRow() != null && position.getGridCol() != null) {
                String key = position.getGridRow() + "," + position.getGridCol();
                matrix.put(key, position);
            }
        }

        return matrix;
    }

    /**
     * 计算统计信息
     */
    private GridLayoutData.GridStatistics calculateStatistics(
        OtoDiamondGridConfigVo gridConfig,
        List<OtoDiamondPositionVo> positions) {

        GridLayoutData.GridStatistics statistics = new GridLayoutData.GridStatistics();

        // 计算总位置数
        int totalPositions = 0;
        if (gridConfig.getRowConfigs() != null) {
            totalPositions = gridConfig.getRowConfigs().stream()
                .mapToInt(OtoDiamondGridConfigVo.RowConfig::getColumnCount)
                .sum();
        }

        // 计算已使用位置数
        int usedPositions = (int) positions.stream()
            .filter(pos -> pos.getGridRow() != null && pos.getGridCol() != null)
            .count();

        // 计算可用位置数
        int availablePositions = totalPositions - usedPositions;

        // 计算使用率
        double usageRate = totalPositions > 0 ? (double) usedPositions / totalPositions : 0.0;

        statistics.setTotalPositions(totalPositions);
        statistics.setUsedPositions(usedPositions);
        statistics.setAvailablePositions(availablePositions);
        statistics.setUsageRate(usageRate);

        return statistics;
    }

    /**
     * 转换VO到模型
     */
    private DiamondGridConfigModel convertToModel(OtoDiamondGridConfigVo vo) {
        DiamondGridConfigModel model = new DiamondGridConfigModel();
        model.setId(vo.getId());
        model.setTotalRows(vo.getTotalRows());
        model.setGridGap(vo.getGridGap());
        model.setIsActive(vo.getIsActive());
        model.setDescription(vo.getDescription());
        model.setCreateTime(vo.getCreateTime());
        model.setUpdateTime(vo.getUpdateTime());

        // 转换行配置
        if (vo.getRowConfigs() != null) {
            List<DiamondGridConfigModel.RowConfigModel> rowConfigs = vo.getRowConfigs().stream()
                .map(this::convertRowConfigToModel)
                .collect(Collectors.toList());
            model.setRowConfigs(rowConfigs);
        }

        return model;
    }

    /**
     * 转换行配置到模型
     */
    private DiamondGridConfigModel.RowConfigModel convertRowConfigToModel(OtoDiamondGridConfigVo.RowConfig vo) {
        DiamondGridConfigModel.RowConfigModel model = new DiamondGridConfigModel.RowConfigModel();
        model.setRowIndex(vo.getRowIndex());
        model.setColumnCount(vo.getColumnCount());
        model.setRowHeight(vo.getRowHeight());
        model.setSortOrder(vo.getSortOrder());
        return model;
    }

    /**
     * 获取已占用的位置
     */
    private Set<String> getOccupiedPositions() {
        List<OtoDiamondPositionVo> usedPositions = diamondPositionService.queryEnabledList().stream()
            .filter(pos -> pos.getGridRow() != null && pos.getGridCol() != null)
            .collect(Collectors.toList());

        return usedPositions.stream()
            .map(pos -> pos.getGridRow() + "," + pos.getGridCol())
            .collect(Collectors.toSet());
    }

    /**
     * 生成可用位置列表
     */
    private List<String> generateAvailablePositions(OtoDiamondGridConfigVo gridConfig, Set<String> occupiedPositions) {
        List<String> availablePositions = new ArrayList<>();

        if (gridConfig.getRowConfigs() != null) {
            for (OtoDiamondGridConfigVo.RowConfig rowConfig : gridConfig.getRowConfigs()) {
                for (int col = 1; col <= rowConfig.getColumnCount(); col++) {
                    String position = rowConfig.getRowIndex() + "," + col;
                    if (!occupiedPositions.contains(position)) {
                        availablePositions.add(position);
                    }
                }
            }
        }

        return availablePositions;
    }

    /**
     * 转换模型到VO
     */
    private OtoDiamondGridConfigVo convertToVo(DiamondGridConfigModel model) {
        OtoDiamondGridConfigVo vo = new OtoDiamondGridConfigVo();
        vo.setId(model.getId());
        vo.setTotalRows(model.getTotalRows());
        vo.setGridGap(model.getGridGap());
        vo.setIsActive(model.getIsActive());
        vo.setDescription(model.getDescription());
        vo.setCreateTime(model.getCreateTime());
        vo.setUpdateTime(model.getUpdateTime());
        vo.setCreateBy(model.getCreateBy());
        vo.setUpdateBy(model.getUpdateBy());

        // 转换行配置
        if (model.getRowConfigs() != null) {
            List<OtoDiamondGridConfigVo.RowConfig> rowConfigs = model.getRowConfigs().stream()
                .map(this::convertRowConfigToVo)
                .collect(Collectors.toList());
            vo.setRowConfigs(rowConfigs);
        }

        return vo;
    }

    /**
     * 转换行配置模型到VO
     */
    private OtoDiamondGridConfigVo.RowConfig convertRowConfigToVo(DiamondGridConfigModel.RowConfigModel model) {
        OtoDiamondGridConfigVo.RowConfig vo = new OtoDiamondGridConfigVo.RowConfig();
        vo.setRowIndex(model.getRowIndex());
        vo.setColumnCount(model.getColumnCount());
        vo.setRowHeight(model.getRowHeight());
        vo.setSortOrder(model.getSortOrder());
        return vo;
    }

}
