package com.oto.front.config.service;

import com.oto.common.domain.vo.OtoConfigVo;
import com.oto.common.domain.bo.OtoConfigBo;
import com.oto.common.mybatis.core.page.TableDataInfo;
import com.oto.common.mybatis.core.page.PageQuery;
import com.oto.constantandenums.enums.OtoConfigKeyEnums;

import java.util.Collection;
import java.util.List;

/**
 * oto参数配置Service接口
 * oto-front-system-config模块的配置服务接口
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
public interface OtoConfigService {

    /**
     * 查询oto参数配置
     *
     * @param configId 主键
     * @return oto参数配置
     */
    OtoConfigVo queryById(Long configId);

    /**
     * 分页查询oto参数配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return oto参数配置分页列表
     */
    TableDataInfo<OtoConfigVo> queryPageList(OtoConfigBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的oto参数配置列表
     *
     * @param bo 查询条件
     * @return oto参数配置列表
     */
    List<OtoConfigVo> queryList(OtoConfigBo bo);

    /**
     * 新增oto参数配置
     *
     * @param bo oto参数配置
     * @return 新增参数的值
     */
    String insertByBo(OtoConfigBo bo);

    /**
     * 修改oto参数配置
     *
     * @param bo oto参数配置
     * @return 修改后的参数值
     */
    String updateByBo(OtoConfigBo bo);

    /**
     * 校验并批量删除oto参数配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据参数键名获取参数键值
     *
     * @param configKey 参数键名
     * @return 参数键值
     */
    String getConfigValueByKey(String configKey);

    /**
     * 刷新所有配置缓存
     *
     * @return 是否刷新成功
     */
    Boolean refreshOtoAllConfigCache();

    Boolean getEnableByConfigKey(OtoConfigKeyEnums otoConfigKeyEnums);
}
