<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.oto.front.config.mapper.OtoDiamondPositionMapper">

    <resultMap type="com.oto.common.domain.vo.OtoDiamondPositionVo" id="OtoDiamondPositionResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="icon" column="icon"/>
        <result property="iconType" column="icon_type"/>
        <result property="url" column="url"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="gridRow" column="grid_row"/>
        <result property="gridCol" column="grid_col"/>
        <result property="spanRows" column="span_rows"/>
        <result property="spanCols" column="span_cols"/>
        <result property="status" column="status"/>
        <result property="description" column="description"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>

    </resultMap>

    <!-- 查询所有启用的金刚位（按网格位置排序） -->
    <select id="selectEnabledList" resultMap="OtoDiamondPositionResult">
        SELECT
            id, name, icon, icon_type, url, sort_order,
            grid_row, grid_col, span_rows, span_cols,
            status, description,
            create_time, update_time, create_by, update_by, tenant_id, create_dept, del_flag
        FROM oto_diamond_position
        WHERE del_flag = '0'
          AND status = 1
        ORDER BY
            CASE WHEN grid_row IS NOT NULL AND grid_col IS NOT NULL
                 THEN grid_row * 100 + grid_col
                 ELSE sort_order * 10000 + id
            END ASC
    </select>

    <!-- 获取最大排序序号 -->
    <select id="selectMaxSortOrder" resultType="java.lang.Integer">
        SELECT COALESCE(MAX(sort_order), 0)
        FROM oto_diamond_position
        WHERE del_flag = '0'
    </select>

    <!-- 批量更新状态 -->
    <update id="batchUpdateStatus">
        UPDATE oto_diamond_position
        SET status = #{status}, update_time = NOW()
        WHERE del_flag = '0'
          AND id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量更新排序 -->
    <update id="batchUpdateSortOrder">
        UPDATE oto_diamond_position
        SET sort_order = CASE id
        <foreach collection="items" item="item">
            WHEN #{item.id} THEN #{item.sortOrder}
        </foreach>
        END,
        update_time = NOW()
        WHERE del_flag = '0'
          AND id IN
        <foreach collection="items" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <!-- 批量更新网格位置 -->
    <update id="batchUpdateGridPositions">
        UPDATE oto_diamond_position
        SET grid_row = CASE id
        <foreach collection="positions" item="position">
            WHEN #{position.id} THEN #{position.gridRow}
        </foreach>
        END,
        grid_col = CASE id
        <foreach collection="positions" item="position">
            WHEN #{position.id} THEN #{position.gridCol}
        </foreach>
        END,
        update_time = NOW()
        WHERE del_flag = '0'
          AND id IN
        <foreach collection="positions" item="position" open="(" separator="," close=")">
            #{position.id}
        </foreach>
    </update>

    <!-- 检查网格位置冲突 -->
    <select id="selectPositionConflicts" resultType="java.util.Map">
        SELECT
            CONCAT(grid_row, ',', grid_col) as position,
            GROUP_CONCAT(id) as conflict_ids,
            COUNT(*) as conflict_count
        FROM oto_diamond_position
        WHERE del_flag = '0'
          AND grid_row IS NOT NULL
          AND grid_col IS NOT NULL
          AND (grid_row, grid_col) IN
        <foreach collection="positions" item="position" open="(" separator="," close=")">
            (#{position.gridRow}, #{position.gridCol})
        </foreach>
        GROUP BY grid_row, grid_col
        HAVING COUNT(*) > 1
    </select>

    <!-- 查询指定网格位置的金刚位 -->
    <select id="selectByGridPosition" resultMap="OtoDiamondPositionResult">
        SELECT
            id, name, icon, icon_type, url, sort_order,
            grid_row, grid_col, span_rows, span_cols,
            status, description,
            create_time, update_time, create_by, update_by, tenant_id, create_dept, del_flag
        FROM oto_diamond_position
        WHERE del_flag = '0'
          AND grid_row = #{gridRow}
          AND grid_col = #{gridCol}
        LIMIT 1
    </select>

</mapper>
