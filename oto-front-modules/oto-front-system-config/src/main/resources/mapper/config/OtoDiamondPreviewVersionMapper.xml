<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.oto.front.config.mapper.OtoDiamondPreviewVersionMapper">

    <resultMap type="com.oto.common.domain.vo.OtoDiamondPreviewVersionVo" id="OtoDiamondPreviewVersionResult">
        <result property="id" column="id"/>
        <result property="versionName" column="version_name"/>
        <result property="versionCode" column="version_code"/>
        <result property="gridConfig" column="grid_config"/>
        <result property="positionsConfig" column="positions_config"/>
        <result property="previewUrl" column="preview_url"/>
        <result property="status" column="status"/>
        <result property="isCurrent" column="is_current"/>
        <result property="publishTime" column="publish_time"/>
        <result property="stopTime" column="stop_time"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <!-- 获取当前发布版本 -->
    <select id="selectCurrentVersion" resultMap="OtoDiamondPreviewVersionResult">
        SELECT
            id, version_name, version_code, grid_config, positions_config, preview_url,
            status, is_current, publish_time, stop_time,
            create_time, update_time, create_by, update_by, tenant_id, create_dept, del_flag
        FROM oto_diamond_preview_version
        WHERE is_current = 1
          AND status = 2
          AND del_flag = '0'
        ORDER BY publish_time DESC
        LIMIT 1
    </select>

    <!-- 根据版本编码查询 -->
    <select id="selectByVersionCode" resultMap="OtoDiamondPreviewVersionResult">
        SELECT
            id, version_name, version_code, grid_config, positions_config, preview_url,
            status, is_current, publish_time, stop_time,
            create_time, update_time, create_by, update_by, tenant_id, create_dept, del_flag
        FROM oto_diamond_preview_version
        WHERE version_code = #{versionCode}
          AND del_flag = '0'
    </select>

    <!-- 更新当前发布版本的停止时间 -->
    <update id="updateStopTimeForCurrentVersion">
        UPDATE oto_diamond_preview_version
        SET stop_time = #{stopTime},
            update_time = NOW()
        WHERE is_current = 1
          AND status = 2
          AND del_flag = '0'
    </update>

    <!-- 清除所有版本的当前状态 -->
    <update id="clearCurrentStatus">
        UPDATE oto_diamond_preview_version
        SET is_current = 0,
            update_time = NOW()
        WHERE del_flag = '0'
    </update>

    <!-- 设置指定版本为当前版本 -->
    <update id="setCurrentVersion">
        UPDATE oto_diamond_preview_version
        SET is_current = 1,
            update_time = NOW()
        WHERE version_code = #{versionCode}
          AND del_flag = '0'
    </update>

    <!-- 更新版本状态 -->
    <update id="updateVersionStatus">
        UPDATE oto_diamond_preview_version
        SET status = #{status},
            update_time = NOW()
        WHERE version_code = #{versionCode}
          AND del_flag = '0'
    </update>

    <!-- 批量更新版本状态 -->
    <update id="batchUpdateStatus">
        UPDATE oto_diamond_preview_version
        SET status = #{status},
            update_time = NOW()
        WHERE del_flag = '0'
          AND id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 获取指定状态的版本列表 -->
    <select id="selectByStatus" resultMap="OtoDiamondPreviewVersionResult">
        SELECT
            id, version_name, version_code, grid_config, positions_config, preview_url,
            status, is_current, publish_time, stop_time,
            create_time, update_time, create_by, update_by, tenant_id, create_dept, del_flag
        FROM oto_diamond_preview_version
        WHERE status = #{status}
          AND del_flag = '0'
        ORDER BY create_time DESC
    </select>

    <!-- 检查版本编码是否存在 -->
    <select id="countByVersionCode" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM oto_diamond_preview_version
        WHERE version_code = #{versionCode}
          AND del_flag = '0'
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 获取最新的版本编码 -->
    <select id="selectLatestVersionCode" resultType="java.lang.String">
        SELECT version_code
        FROM oto_diamond_preview_version
        WHERE del_flag = '0'
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 统计各状态版本数量 -->
    <select id="countByStatusGroup" resultType="java.util.Map">
        SELECT
            status,
            COUNT(*) as count
        FROM oto_diamond_preview_version
        WHERE del_flag = '0'
        GROUP BY status
    </select>

    <!-- 删除过期的草稿版本（保留最近N个） -->
    <delete id="deleteExpiredDrafts">
        DELETE FROM oto_diamond_preview_version
        WHERE status = 0
          AND del_flag = '0'
          AND id NOT IN (
              SELECT id FROM (
                  SELECT id
                  FROM oto_diamond_preview_version
                  WHERE status = 0 AND del_flag = '0'
                  ORDER BY create_time DESC
                  LIMIT #{keepCount}
              ) t
          )
    </delete>

</mapper>
