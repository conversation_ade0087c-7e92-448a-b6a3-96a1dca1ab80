<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.oto</groupId>
        <artifactId>oto-front-modules</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>oto-front-system-config</artifactId>

    <description>
        oto-front系统配置管理模块（金刚位配置等）
    </description>

    <dependencies>
        <!-- 通用工具-->
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-translation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-web</artifactId>
        </dependency>

        <!-- 远程调用领域模型 -->
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-remote-domain</artifactId>
        </dependency>

        <!-- HTTP客户端模块 -->
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-http-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-satoken</artifactId>
        </dependency>

        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-tenant</artifactId>
        </dependency>

        <!-- 参数验证 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-redis</artifactId>
        </dependency>

        <!-- 通用远程调用领域模型 -->
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-remote-domain</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-http-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-api</artifactId>
        </dependency>
    </dependencies>

</project>
