package com.oto.member.service.impl;

import com.oto.member.domain.bo.OtoUserRegisterInfoBo;
import com.oto.member.domain.entity.OtoUserRegisterInfo;
import com.oto.member.domain.vo.OtoUserRegisterInfoVo;
import com.oto.common.core.utils.MapstructUtils;
import com.oto.common.core.utils.StringUtils;
import com.oto.common.mybatis.core.page.TableDataInfo;
import com.oto.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.oto.member.mapper.RegisterManageMapper;
import com.oto.member.service.RegisterManageService;

import java.util.List;
import java.util.Collection;

/**
 * 用户注册日志（合规留痕/风控专用）Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class RegisterManageServiceImpl implements RegisterManageService {

    private final RegisterManageMapper baseMapper;

    /**
     * 查询用户注册日志（合规留痕/风控专用）
     *
     * @param id 主键
     * @return 用户注册日志（合规留痕/风控专用）
     */
    @Override
    public OtoUserRegisterInfoVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询用户注册日志（合规留痕/风控专用）列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户注册日志（合规留痕/风控专用）分页列表
     */
    @Override
    public TableDataInfo<OtoUserRegisterInfoVo> queryPageList(OtoUserRegisterInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OtoUserRegisterInfo> lqw = buildQueryWrapper(bo);
        Page<OtoUserRegisterInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的用户注册日志（合规留痕/风控专用）列表
     *
     * @param bo 查询条件
     * @return 用户注册日志（合规留痕/风控专用）列表
     */
    @Override
    public List<OtoUserRegisterInfoVo> queryList(OtoUserRegisterInfoBo bo) {
        LambdaQueryWrapper<OtoUserRegisterInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OtoUserRegisterInfo> buildQueryWrapper(OtoUserRegisterInfoBo bo) {
        LambdaQueryWrapper<OtoUserRegisterInfo> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(OtoUserRegisterInfo::getId);
        lqw.eq(bo.getUserId() != null, OtoUserRegisterInfo::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getEncryptPhone()), OtoUserRegisterInfo::getEncryptPhone, bo.getEncryptPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getPhoneHash()), OtoUserRegisterInfo::getPhoneHash, bo.getPhoneHash());
        lqw.eq(StringUtils.isNotBlank(bo.getRegisterIp()), OtoUserRegisterInfo::getRegisterIp, bo.getRegisterIp());
        lqw.eq(StringUtils.isNotBlank(bo.getRegisterDevice()), OtoUserRegisterInfo::getRegisterDevice, bo.getRegisterDevice());
        lqw.eq(StringUtils.isNotBlank(bo.getRegisterChannel()), OtoUserRegisterInfo::getRegisterChannel, bo.getRegisterChannel());
        lqw.eq(StringUtils.isNotBlank(bo.getSmsCode()), OtoUserRegisterInfo::getSmsCode, bo.getSmsCode());
        lqw.eq(StringUtils.isNotBlank(bo.getInviteCode()), OtoUserRegisterInfo::getInviteCode, bo.getInviteCode());
        lqw.eq(bo.getRegisterStatus() != null, OtoUserRegisterInfo::getRegisterStatus, bo.getRegisterStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getFailReason()), OtoUserRegisterInfo::getFailReason, bo.getFailReason());
        lqw.eq(StringUtils.isNotBlank(bo.getUserAgent()), OtoUserRegisterInfo::getUserAgent, bo.getUserAgent());
        lqw.eq(StringUtils.isNotBlank(bo.getGeoLocation()), OtoUserRegisterInfo::getGeoLocation, bo.getGeoLocation());
        lqw.eq(StringUtils.isNotBlank(bo.getEncryptIv()), OtoUserRegisterInfo::getEncryptIv, bo.getEncryptIv());
        lqw.eq(StringUtils.isNotBlank(bo.getCountryCode()), OtoUserRegisterInfo::getCountryCode, bo.getCountryCode());
        lqw.eq(StringUtils.isNotBlank(bo.getEncryptionVersion()), OtoUserRegisterInfo::getEncryptionVersion, bo.getEncryptionVersion());
        return lqw;
    }

    /**
     * 新增用户注册日志（合规留痕/风控专用）
     *
     * @param bo 用户注册日志（合规留痕/风控专用）
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(OtoUserRegisterInfoBo bo) {
        OtoUserRegisterInfo add = MapstructUtils.convert(bo, OtoUserRegisterInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改用户注册日志（合规留痕/风控专用）
     *
     * @param bo 用户注册日志（合规留痕/风控专用）
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OtoUserRegisterInfoBo bo) {
        OtoUserRegisterInfo update = MapstructUtils.convert(bo, OtoUserRegisterInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OtoUserRegisterInfo entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除用户注册日志（合规留痕/风控专用）信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
