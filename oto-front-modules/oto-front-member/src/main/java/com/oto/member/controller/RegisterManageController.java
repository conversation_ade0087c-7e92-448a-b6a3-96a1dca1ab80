package com.oto.member.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.oto.common.idempotent.annotation.RepeatSubmit;
import com.oto.common.log.annotation.Log;
import com.oto.common.web.core.BaseController;
import com.oto.common.mybatis.core.page.PageQuery;
import com.oto.common.core.domain.R;
import com.oto.common.core.validate.AddGroup;
import com.oto.common.core.validate.EditGroup;
import com.oto.common.log.enums.BusinessType;
import com.oto.common.excel.utils.ExcelUtil;
import com.oto.member.domain.vo.OtoUserRegisterInfoVo;
import com.oto.member.domain.bo.OtoUserRegisterInfoBo;
import com.oto.member.service.RegisterManageService;
import com.oto.common.mybatis.core.page.TableDataInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 用户注册日志管理控制器
 * 提供用户注册日志的查询、导出等功能，用于合规留痕和风控分析
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
@Tag(name = "用户注册日志管理", description = "用户注册日志的查询、导出等功能，用于合规留痕和风控分析")
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/member/userRegisterInfo")
public class RegisterManageController extends BaseController {

    private final RegisterManageService otoUserRegisterInfoService;

    /**
     * 查询用户注册日志（合规留痕/风控专用）列表
     */
    @Operation(summary = "查询用户注册日志列表", description = "分页查询用户注册日志信息，用于合规留痕和风控分析")
    @SaCheckPermission("member:userRegisterInfo:list")
    @GetMapping("/list")
    public TableDataInfo<OtoUserRegisterInfoVo> list(OtoUserRegisterInfoBo bo, PageQuery pageQuery) {
        return otoUserRegisterInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出用户注册日志（合规留痕/风控专用）列表
     */
    @Operation(summary = "导出用户注册日志", description = "导出用户注册日志数据到Excel文件")
    @SaCheckPermission("member:userRegisterInfo:export")
    @Log(title = "用户注册日志（合规留痕/风控专用）", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OtoUserRegisterInfoBo bo, HttpServletResponse response) {
        List<OtoUserRegisterInfoVo> list = otoUserRegisterInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "用户注册日志（合规留痕/风控专用）", OtoUserRegisterInfoVo.class, response);
    }

    /**
     * 获取用户注册日志（合规留痕/风控专用）详细信息
     *
     * @param id 主键
     */
    @Operation(summary = "获取用户注册日志详情", description = "根据ID获取用户注册日志的详细信息")
    @Parameter(name = "id", description = "用户注册日志ID", required = true)
    @SaCheckPermission("member:userRegisterInfo:query")
    @GetMapping("/{id}")
    public R<OtoUserRegisterInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(otoUserRegisterInfoService.queryById(id));
    }

    /**
     * 新增用户注册日志（合规留痕/风控专用）
     */
    @Operation(summary = "新增用户注册日志", description = "创建新的用户注册日志记录")
    @SaCheckPermission("member:userRegisterInfo:add")
    @Log(title = "用户注册日志（合规留痕/风控专用）", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OtoUserRegisterInfoBo bo) {
        return toAjax(otoUserRegisterInfoService.insertByBo(bo));
    }

    /**
     * 修改用户注册日志（合规留痕/风控专用）
     */
    @Operation(summary = "修改用户注册日志", description = "更新用户注册日志信息")
    @SaCheckPermission("member:userRegisterInfo:edit")
    @Log(title = "用户注册日志（合规留痕/风控专用）", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OtoUserRegisterInfoBo bo) {
        return toAjax(otoUserRegisterInfoService.updateByBo(bo));
    }

    /**
     * 删除用户注册日志（合规留痕/风控专用）
     *
     * @param ids 主键串
     */
    @Operation(summary = "删除用户注册日志", description = "批量删除用户注册日志记录")
    @Parameter(name = "ids", description = "用户注册日志ID数组", required = true)
    @SaCheckPermission("member:userRegisterInfo:remove")
    @Log(title = "用户注册日志（合规留痕/风控专用）", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(otoUserRegisterInfoService.deleteWithValidByIds(List.of(ids), true));
    }
}
