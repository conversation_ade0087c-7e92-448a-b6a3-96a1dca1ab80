package com.oto.member.service;

import com.oto.member.domain.bo.OtoUserRegisterInfoBo;
import com.oto.member.domain.vo.OtoUserRegisterInfoVo;
import com.oto.common.mybatis.core.page.TableDataInfo;
import com.oto.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 用户注册日志管理Service接口
 * 提供用户注册日志的查询、新增、修改、删除等功能，用于合规留痕和风控分析
 *
 * <AUTHOR> Li
 * @date 2025-07-06
 */
public interface RegisterManageService {

    /**
     * 查询用户注册日志（合规留痕/风控专用）
     *
     * @param id 主键
     * @return 用户注册日志（合规留痕/风控专用）
     */
    OtoUserRegisterInfoVo queryById(Long id);

    /**
     * 分页查询用户注册日志（合规留痕/风控专用）列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户注册日志（合规留痕/风控专用）分页列表
     */
    TableDataInfo<OtoUserRegisterInfoVo> queryPageList(OtoUserRegisterInfoBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的用户注册日志（合规留痕/风控专用）列表
     *
     * @param bo 查询条件
     * @return 用户注册日志（合规留痕/风控专用）列表
     */
    List<OtoUserRegisterInfoVo> queryList(OtoUserRegisterInfoBo bo);

    /**
     * 新增用户注册日志（合规留痕/风控专用）
     *
     * @param bo 用户注册日志（合规留痕/风控专用）
     * @return 是否新增成功
     */
    Boolean insertByBo(OtoUserRegisterInfoBo bo);

    /**
     * 修改用户注册日志（合规留痕/风控专用）
     *
     * @param bo 用户注册日志（合规留痕/风控专用）
     * @return 是否修改成功
     */
    Boolean updateByBo(OtoUserRegisterInfoBo bo);

    /**
     * 校验并批量删除用户注册日志（合规留痕/风控专用）信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
