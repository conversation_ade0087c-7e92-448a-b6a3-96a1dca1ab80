package com.oto.front.system.feign;

import com.oto.common.core.domain.R;
import com.oto.common.http.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * 管理后台配置服务 FeignClient
 * 用于 oto-front 调用 oto-admin 的远程服务
 * 
 * 调用目标：oto-admin 的 TestController（端口 8090）
 * 
 * <AUTHOR>
 * @date 2025-08-31
 */
@FeignClient(
    name = "admin-config-service",
    url = "${oto.services.admin.base-url:http://localhost:8080}",
    path = "/test",  // 对应 TestController 的 @RequestMapping("/test")
    configuration = FeignConfig.class
)
public interface AdminConfigFeignClient {

    /**
     * 调用管理后台的测试接口
     * 对应 TestController.hello() 方法
     * 
     * 注意：这个接口只能通过 oto-admin 的远程服务端口（8090）访问
     */
    @GetMapping("/hello")
    R<String> getTestHello();

    /**
     * 调用管理后台的远程服务接口
     * 对应 TestController.remoteService() 方法
     */
    @GetMapping("/remote-service")
    R<String> getRemoteService();

    /**
     * 获取管理后台服务信息
     * 对应 TestController.getServiceInfo() 方法
     */
    @GetMapping("/info")
    R<Object> getServiceInfo();

    /**
     * 健康检查
     * 对应 TestController.health() 方法
     */
    @GetMapping("/health")
    R<String> healthCheck();
}
