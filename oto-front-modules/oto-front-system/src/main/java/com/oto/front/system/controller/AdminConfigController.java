package com.oto.front.system.controller;

import com.oto.common.core.domain.R;
import com.oto.front.system.service.AdminConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 管理后台配置控制器
 * 演示正确的远程服务调用架构
 * 
 * 正确的调用链：
 * Controller -> Service -> FeignClient -> 远程服务
 * 
 * 错误的调用链（被安全检查器阻止）：
 * Controller -> FeignClient -> 远程服务
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@Slf4j
@RestController
@RequestMapping("/api/admin-config")
@RequiredArgsConstructor
public class AdminConfigController {

    private final AdminConfigService adminConfigService;

    /**
     * 获取管理后台测试信息
     * 演示通过 Service 层调用远程服务
     */
    @GetMapping("/test/hello")
    public R<String> getTestHello() {
        log.info("前端请求获取管理后台测试信息");
        return adminConfigService.getTestHello();
    }

    /**
     * 获取管理后台远程服务信息
     */
    @GetMapping("/remote-service")
    public R<String> getRemoteService() {
        log.info("前端请求获取管理后台远程服务信息");
        return adminConfigService.getRemoteService();
    }

    /**
     * 获取管理后台服务详细信息
     */
    @GetMapping("/service-info")
    public R<Object> getServiceInfo() {
        log.info("前端请求获取管理后台服务详细信息");
        return adminConfigService.getServiceInfo();
    }

    /**
     * 管理后台健康检查
     */
    @GetMapping("/health")
    public R<String> healthCheck() {
        log.info("前端请求管理后台健康检查");
        return adminConfigService.healthCheck();
    }

    /**
     * 综合服务状态检查
     * 提供完整的服务状态报告
     */
    @GetMapping("/status")
    public R<Map<String, Object>> getComprehensiveStatus() {
        log.info("前端请求综合服务状态检查");
        try {
            Map<String, Object> status = adminConfigService.getComprehensiveStatus();
            return R.ok(status);
        } catch (Exception e) {
            log.error("获取综合服务状态失败", e);
            return R.fail("获取服务状态失败，请稍后重试");
        }
    }
}
