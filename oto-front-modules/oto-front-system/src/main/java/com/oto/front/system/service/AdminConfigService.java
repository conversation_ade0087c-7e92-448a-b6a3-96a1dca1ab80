package com.oto.front.system.service;

import com.oto.common.core.domain.R;
import com.oto.common.core.exception.ServiceException;
import com.oto.front.system.feign.AdminConfigFeignClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 管理后台配置服务
 * 封装对 AdminConfigFeignClient 的调用，提供业务逻辑处理
 * 
 * 注意：这是正确的架构模式
 * Controller -> Service -> FeignClient
 * 而不是 Controller -> FeignClient（违反安全规范）
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AdminConfigService {

    private final AdminConfigFeignClient adminConfigFeignClient;

    /**
     * 获取管理后台测试信息
     * 
     * @return 测试信息
     */
    public R<String> getTestHello() {
        try {
            log.debug("调用管理后台测试接口");
            R<String> result = adminConfigFeignClient.getTestHello();
            
            if (!R.isSuccess(result)) {
                log.warn("调用管理后台测试接口失败: {}", result.getMsg());
                throw new ServiceException("获取测试信息失败: " + result.getMsg());
            }
            
            log.info("成功获取管理后台测试信息: {}", result.getData());
            return result;
        } catch (Exception e) {
            log.error("调用管理后台测试接口异常", e);
            throw new ServiceException("获取测试信息失败，请稍后重试");
        }
    }

    /**
     * 获取管理后台远程服务信息
     * 
     * @return 远程服务信息
     */
    public R<String> getRemoteService() {
        try {
            log.debug("调用管理后台远程服务接口");
            R<String> result = adminConfigFeignClient.getRemoteService();
            
            if (!R.isSuccess(result)) {
                log.warn("调用管理后台远程服务接口失败: {}", result.getMsg());
                throw new ServiceException("获取远程服务信息失败: " + result.getMsg());
            }
            
            log.info("成功获取管理后台远程服务信息: {}", result.getData());
            return result;
        } catch (Exception e) {
            log.error("调用管理后台远程服务接口异常", e);
            throw new ServiceException("获取远程服务信息失败，请稍后重试");
        }
    }

    /**
     * 获取管理后台服务详细信息
     * 
     * @return 服务详细信息
     */
    public R<Object> getServiceInfo() {
        try {
            log.debug("调用管理后台服务信息接口");
            R<Object> result = adminConfigFeignClient.getServiceInfo();
            
            if (!R.isSuccess(result)) {
                log.warn("调用管理后台服务信息接口失败: {}", result.getMsg());
                throw new ServiceException("获取服务信息失败: " + result.getMsg());
            }
            
            log.info("成功获取管理后台服务信息");
            return result;
        } catch (Exception e) {
            log.error("调用管理后台服务信息接口异常", e);
            throw new ServiceException("获取服务信息失败，请稍后重试");
        }
    }

    /**
     * 管理后台健康检查
     * 
     * @return 健康状态
     */
    public R<String> healthCheck() {
        try {
            log.debug("调用管理后台健康检查接口");
            R<String> result = adminConfigFeignClient.healthCheck();
            
            if (!R.isSuccess(result)) {
                log.warn("管理后台健康检查失败: {}", result.getMsg());
                return R.fail("管理后台服务不可用");
            }
            
            log.info("管理后台健康检查通过: {}", result.getData());
            return result;
        } catch (Exception e) {
            log.error("管理后台健康检查异常", e);
            return R.fail("管理后台服务连接失败");
        }
    }

    /**
     * 综合服务状态检查
     * 包含健康检查和基本功能验证
     * 
     * @return 综合服务状态
     */
    public Map<String, Object> getComprehensiveStatus() {
        try {
            log.info("开始综合服务状态检查");
            
            // 健康检查
            R<String> healthResult = healthCheck();
            boolean isHealthy = R.isSuccess(healthResult);
            
            // 功能检查
            boolean isFunctional = false;
            String functionalMessage = "";
            try {
                R<String> testResult = getTestHello();
                isFunctional = R.isSuccess(testResult);
                functionalMessage = isFunctional ? "功能正常" : "功能异常";
            } catch (Exception e) {
                functionalMessage = "功能检查失败: " + e.getMessage();
            }
            
            // 构建状态报告
            Map<String, Object> status = Map.of(
                "healthy", isHealthy,
                "functional", isFunctional,
                "healthMessage", isHealthy ? healthResult.getData() : healthResult.getMsg(),
                "functionalMessage", functionalMessage,
                "overallStatus", isHealthy && isFunctional ? "正常" : "异常",
                "timestamp", System.currentTimeMillis()
            );
            
            log.info("综合服务状态检查完成: {}", status.get("overallStatus"));
            return status;
            
        } catch (Exception e) {
            log.error("综合服务状态检查异常", e);
            return Map.of(
                "healthy", false,
                "functional", false,
                "healthMessage", "检查失败",
                "functionalMessage", "检查失败: " + e.getMessage(),
                "overallStatus", "异常",
                "timestamp", System.currentTimeMillis()
            );
        }
    }
}
