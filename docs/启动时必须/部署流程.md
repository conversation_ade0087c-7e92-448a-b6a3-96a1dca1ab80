### 修复1: 手动生成证书
**请您先执行以下命令生成SSL证书：**

```bash
# 创建目录
mkdir -p ~/.oto/ssl/

# 生成oto-front证书
keytool -genkeypair -alias oto-front-service \
  -keyalg RSA -keysize 2048 -validity 365 \
  -keystore ~/.oto/ssl/oto-front.p12 \
  -storetype PKCS12 \
  -storepass oto-ssl-2025 \
  -keypass oto-ssl-2025 \
  -dname "CN=localhost, OU=OTO-Front, O=OTO, L=Shanghai, ST=Shanghai, C=CN" \
  -ext SAN=dns:localhost,ip:127.0.0.1

# 生成oto-admin证书
keytool -genkeypair -alias oto-admin-service \
  -keyalg RSA -keysize 2048 -validity 365 \
  -keystore ~/.oto/ssl/oto-admin.p12 \
  -storetype PKCS12 \
  -storepass oto-ssl-2025 \
  -keypass oto-ssl-2025 \
  -dname "CN=localhost, OU=OTO-Admin, O=OTO, L=Shanghai, ST=Shanghai, C=CN" \
  -ext SAN=dns:localhost,ip:127.0.0.1

# 验证证书
ls -la ~/.oto/ssl/
```