<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>oto-home</artifactId>
        <groupId>com.oto</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>oto-common-modules</artifactId>
    <packaging>pom</packaging>

    <name>oto-common-modules</name>
    <description>OTO通用业务模块集合</description>

    <modules>
        <!-- 这里可以添加通用业务模块 -->
        <module>oto-common-http-client</module>
        <module>oto-common-remote-domain</module>
        <module>remote-front-api</module>
    </modules>

</project>
