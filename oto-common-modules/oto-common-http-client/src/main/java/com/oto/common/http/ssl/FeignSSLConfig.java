package com.oto.common.http.ssl;

import feign.Client;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.net.ssl.*;
import java.security.cert.X509Certificate;

/**
 * FeignClient SSL 配置
 * 自动配置 FeignClient 信任自动生成的证书
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Slf4j
@Configuration
@ConditionalOnClass(Client.class)
@ConditionalOnProperty(name = "oto.common.http.ssl.auto-generate", havingValue = "true", matchIfMissing = true)
public class FeignSSLConfig {

    /**
     * 配置 FeignClient 使用自定义 SSL 上下文
     */
    @Bean
    public Client feignClient() {
        try {
            log.info("🔒 配置FeignClient信任自签名证书...");
            
            // 创建信任所有证书的SSL上下文
            SSLContext sslContext = createTrustAllSSLContext();
            
            // 创建信任所有主机名的验证器
            HostnameVerifier hostnameVerifier = createTrustAllHostnameVerifier();
            
            log.info("✅ FeignClient SSL配置完成，信任所有自签名证书");
            
            return new Client.Default(sslContext.getSocketFactory(), hostnameVerifier);
            
        } catch (Exception e) {
            log.error("❌ FeignClient SSL 配置失败，使用默认配置", e);
            return new Client.Default(null, null);
        }
    }

    /**
     * 创建信任所有证书的SSL上下文
     */
    private SSLContext createTrustAllSSLContext() throws Exception {
        // 创建信任所有证书的TrustManager
        TrustManager[] trustAllCerts = new TrustManager[] {
            new X509TrustManager() {
                @Override
                public void checkClientTrusted(X509Certificate[] chain, String authType) {
                    // 信任所有客户端证书
                    log.debug("🔒 信任客户端证书: {}", chain[0].getSubjectX500Principal());
                }

                @Override
                public void checkServerTrusted(X509Certificate[] chain, String authType) {
                    // 信任所有服务端证书
                    log.debug("🔒 信任服务端证书: {}", chain[0].getSubjectX500Principal());
                }

                @Override
                public X509Certificate[] getAcceptedIssuers() {
                    return new X509Certificate[0];
                }
            }
        };

        // 创建SSL上下文
        SSLContext sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, trustAllCerts, new java.security.SecureRandom());
        
        return sslContext;
    }

    /**
     * 创建信任所有主机名的验证器
     */
    private HostnameVerifier createTrustAllHostnameVerifier() {
        return (hostname, session) -> {
            log.debug("🔒 信任主机名: {}", hostname);
            return true; // 信任所有主机名
        };
    }
}
