package com.oto.common.http.security.validator;

import com.oto.common.http.security.RemoteServiceSecurityProperties;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.BufferedReader;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Duration;
import java.util.Base64;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 请求签名验证器
 * 验证请求的数字签名，防止篡改和重放攻击
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SignatureValidator {

    private final RemoteServiceSecurityProperties properties;
    private final RedisTemplate<String, String> redisTemplate;

    /**
     * 验证请求签名
     *
     * @param request HTTP请求
     * @return 验证结果
     */
    public boolean validate(HttpServletRequest request) {
        RemoteServiceSecurityProperties.Signature config = properties.getSignature();
        
        if (!config.isEnabled()) {
            if (config.isLogValidation()) {
                log.debug("✅ 签名验证跳过: 功能已禁用");
            }
            return true;
        }

        try {
            // 1. 获取签名相关头部
            String timestamp = request.getHeader("X-Timestamp");
            String nonce = request.getHeader("X-Nonce");
            String signature = request.getHeader("X-Signature");
            String serviceId = request.getHeader("X-Service-Id");

            if (config.isLogValidation()) {
                log.debug("🔍 签名验证开始: serviceId={}, timestamp={}, nonce={}", 
                         serviceId, timestamp, nonce);
            }

            // 2. 检查必需头部
            if (!validateRequiredHeaders(timestamp, nonce, signature, serviceId)) {
                return false;
            }

            // 3. 时间戳验证 (防重放攻击)
            if (!validateTimestamp(timestamp)) {
                return false;
            }

            // 4. Nonce验证 (防重放攻击)
            if (config.isAntiReplay() && !validateNonce(nonce, serviceId)) {
                return false;
            }

            // 5. 构建签名字符串
            String signString = buildSignString(request, timestamp, nonce, serviceId);

            // 6. 计算期望签名
            String expectedSignature = calculateSignature(signString, config.getSecretKey(), config.getAlgorithm());

            // 7. 详细调试日志
            if (config.isLogValidation()) {
                log.info("🔍 服务端签名验证调试信息:");
                log.info("  服务ID: {}", serviceId);
                log.info("  时间戳: {}", timestamp);
                log.info("  随机数: {}", nonce);
                log.info("  HTTP方法: {}", request.getMethod());
                log.info("  请求URI: {}", request.getRequestURI());
                log.info("  查询参数: {}", normalizeQueryString(request.getQueryString()));
                log.info("  请求体哈希: {}", getRequestBodyHash(request));
                log.info("  构建的签名字符串: [{}]", signString.replace("\n", "\\n"));
                log.info("  计算的期望签名: {}", expectedSignature);
                log.info("  接收到的签名: {}", signature);
                log.info("  密钥: {}", config.getSecretKey());
                log.info("  算法: {}", config.getAlgorithm());
            }

            // 8. 签名比较 (防时序攻击)
            boolean valid = MessageDigest.isEqual(
                signature.getBytes(StandardCharsets.UTF_8),
                expectedSignature.getBytes(StandardCharsets.UTF_8)
            );

            if (valid) {
                // 记录已使用的nonce
                if (config.isAntiReplay()) {
                    recordUsedNonce(nonce, serviceId, config.getTimeout());
                }

                if (config.isLogValidation()) {
                    log.info("✅ 签名验证成功: serviceId={}", serviceId);
                }
            } else {
                log.warn("❌ 签名验证失败: 签名不匹配, serviceId={}", serviceId);
            }

            return valid;

        } catch (Exception e) {
            log.error("❌ 签名验证异常", e);
            return false;
        }
    }

    /**
     * 验证必需的请求头
     */
    private boolean validateRequiredHeaders(String timestamp, String nonce, String signature, String serviceId) {
        List<String> requiredHeaders = properties.getSignature().getRequiredHeaders();
        
        if (requiredHeaders.contains("X-Timestamp") && !StringUtils.hasText(timestamp)) {
            log.warn("❌ 签名验证失败: 缺少 X-Timestamp 头部");
            return false;
        }
        
        if (requiredHeaders.contains("X-Nonce") && !StringUtils.hasText(nonce)) {
            log.warn("❌ 签名验证失败: 缺少 X-Nonce 头部");
            return false;
        }
        
        if (requiredHeaders.contains("X-Signature") && !StringUtils.hasText(signature)) {
            log.warn("❌ 签名验证失败: 缺少 X-Signature 头部");
            return false;
        }
        
        if (requiredHeaders.contains("X-Service-Id") && !StringUtils.hasText(serviceId)) {
            log.warn("❌ 签名验证失败: 缺少 X-Service-Id 头部");
            return false;
        }

        return true;
    }

    /**
     * 验证时间戳
     */
    private boolean validateTimestamp(String timestamp) {
        try {
            long requestTime = Long.parseLong(timestamp);
            long currentTime = System.currentTimeMillis();
            long diff = Math.abs(currentTime - requestTime);
            int timeout = properties.getSignature().getTimeout();

            if (diff > timeout * 1000L) {
                log.warn("❌ 时间戳验证失败: 请求时间戳过期, diff={}ms, timeout={}s", diff, timeout);
                return false;
            }

            return true;
        } catch (NumberFormatException e) {
            log.warn("❌ 时间戳验证失败: 格式错误, timestamp={}", timestamp);
            return false;
        }
    }

    /**
     * 验证Nonce (防重放攻击)
     */
    private boolean validateNonce(String nonce, String serviceId) {
        try {
            String key = buildNonceKey(serviceId, nonce);
            Boolean exists = redisTemplate.hasKey(key);

            if (Boolean.TRUE.equals(exists)) {
                log.warn("❌ Nonce验证失败: 重复的nonce, serviceId={}, nonce={}", serviceId, nonce);
                return false;
            }

            return true;
        } catch (Exception e) {
            log.error("❌ Nonce验证异常", e);
            return false;
        }
    }

    /**
     * 记录已使用的Nonce
     */
    private void recordUsedNonce(String nonce, String serviceId, int timeout) {
        try {
            String key = buildNonceKey(serviceId, nonce);
            redisTemplate.opsForValue().set(key, "used", Duration.ofSeconds(timeout));
        } catch (Exception e) {
            log.warn("记录Nonce失败", e);
        }
    }

    /**
     * 构建Nonce的Redis键
     */
    private String buildNonceKey(String serviceId, String nonce) {
        return "remote_service_nonce:" + serviceId + ":" + nonce;
    }

    /**
     * 构建签名字符串
     */
    private String buildSignString(HttpServletRequest request, String timestamp, String nonce, String serviceId) {
        // 签名字符串格式: HTTP方法\nURI\n查询参数\n请求体\n时间戳\n随机数\n服务ID
        StringBuilder sb = new StringBuilder();
        sb.append(request.getMethod()).append("\n");
        sb.append(request.getRequestURI()).append("\n");
        sb.append(normalizeQueryString(request.getQueryString())).append("\n");
        sb.append(getRequestBodyHash(request)).append("\n");
        sb.append(timestamp).append("\n");
        sb.append(nonce).append("\n");
        sb.append(serviceId);
        
        return sb.toString();
    }

    /**
     * 标准化查询字符串
     */
    private String normalizeQueryString(String queryString) {
        if (!StringUtils.hasText(queryString)) {
            return "";
        }
        
        // 简单处理，实际可能需要更复杂的标准化
        return queryString;
    }

    /**
     * 获取请求体的哈希值
     */
    private String getRequestBodyHash(HttpServletRequest request) {
        try {
            // 读取请求体
            String body = getRequestBody(request);
            if (!StringUtils.hasText(body)) {
                return "";
            }
            
            // 计算SHA256哈希
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(body.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hash);
            
        } catch (Exception e) {
            log.warn("获取请求体哈希失败", e);
            return "";
        }
    }

    /**
     * 读取请求体内容
     */
    private String getRequestBody(HttpServletRequest request) {
        try {
            BufferedReader reader = request.getReader();
            return reader.lines().collect(Collectors.joining("\n"));
        } catch (IOException e) {
            log.warn("读取请求体失败", e);
            return "";
        }
    }

    /**
     * 计算签名
     */
    private String calculateSignature(String data, String secretKey, String algorithm) 
            throws NoSuchAlgorithmException, InvalidKeyException {
        
        Mac mac = Mac.getInstance(algorithm);
        SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), algorithm);
        mac.init(secretKeySpec);
        
        byte[] signature = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(signature);
    }

    /**
     * 获取签名验证统计信息
     */
    public String getValidationStats() {
        RemoteServiceSecurityProperties.Signature config = properties.getSignature();
        return String.format(
            "Signature[enabled=%s, algorithm=%s, timeout=%ds, antiReplay=%s]",
            config.isEnabled(),
            config.getAlgorithm(),
            config.getTimeout(),
            config.isAntiReplay()
        );
    }
}
