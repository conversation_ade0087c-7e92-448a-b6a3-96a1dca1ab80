package com.oto.common.http.config;

import com.oto.common.http.interceptor.SaTokenFeignInterceptor;
import feign.RequestInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Feign 配置类
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class FeignConfig {

    /**
     * Sa-Token OpenFeign 请求拦截器
     * 自动为所有 OpenFeign 客户端添加 Same-Token
     */
    @Bean
    public RequestInterceptor saTokenFeignInterceptor() {
        log.info("🔍 创建Sa-Token Feign拦截器");
        return new SaTokenFeignInterceptor();
    }
}
