package com.oto.common.http.ssl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;

/**
 * 自动 SSL 配置
 * 根据部署环境自动配置 HTTPS
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@Slf4j
@Configuration
@ConditionalOnProperty(name = "oto.common.http.ssl.auto-generate", havingValue = "true", matchIfMissing = true)
public class AutoSSLConfiguration {

    /**
     * 自动 SSL 属性配置
     */
    @Bean
    @ConfigurationProperties(prefix = "oto.common.http.ssl")
    public AutoSSLProperties autoSSLProperties() {
        return new AutoSSLProperties();
    }

    /**
     * SSL 环境检测器
     */
    @Bean
    public SSLEnvironmentDetector sslEnvironmentDetector(Environment environment) {
        return new SSLEnvironmentDetector(environment);
    }

    /**
     * SSL 环境检测器
     */
    public static class SSLEnvironmentDetector {
        
        private final Environment environment;

        public SSLEnvironmentDetector(Environment environment) {
            this.environment = environment;
        }

        /**
         * 检测是否需要自动生成证书
         */
        public boolean shouldAutoGenerateCertificate() {
            // 1. 检查是否明确禁用了自动生成
            if (!environment.getProperty("oto.common.http.ssl.auto-generate", Boolean.class, true)) {
                log.info("🔒 SSL 自动生成已禁用");
                return false;
            }

            // 2. 检查是否已经配置了现有证书
            String existingKeystore = environment.getProperty("server.ssl.key-store");
            if (existingKeystore != null && Files.exists(Paths.get(existingKeystore))) {
                log.info("🔒 检测到现有 SSL 证书: {}", existingKeystore);
                return false;
            }

            // 3. 检查是否是生产环境（生产环境不建议自动生成）
            String[] activeProfiles = environment.getActiveProfiles();
            for (String profile : activeProfiles) {
                if ("prod".equals(profile) || "production".equals(profile)) {
                    log.warn("⚠️ 生产环境不建议使用自动生成的证书，请使用正式 CA 证书");
                    return false;
                }
            }

            // 4. 检查是否是同机器部署
            if (isSameMachineDeployment()) {
                log.info("🔒 检测到同机器部署，启用 SSL 自动生成");
                return true;
            }

            log.info("🔒 非同机器部署，跳过 SSL 自动生成");
            return false;
        }

        /**
         * 检测是否是同机器部署
         */
        private boolean isSameMachineDeployment() {
            // 检查服务地址配置
            String adminBaseUrl = environment.getProperty("oto.services.admin.base-url");
            String frontBaseUrl = environment.getProperty("oto.services.front.base-url");

            // 如果配置了 localhost 或 127.0.0.1，认为是同机器部署
            if (adminBaseUrl != null && (adminBaseUrl.contains("localhost") || adminBaseUrl.contains("127.0.0.1"))) {
                return true;
            }

            if (frontBaseUrl != null && (frontBaseUrl.contains("localhost") || frontBaseUrl.contains("127.0.0.1"))) {
                return true;
            }

            // 检查是否配置了相同的主机名
            String currentHost = environment.getProperty("server.address", "localhost");
            if ("localhost".equals(currentHost) || "127.0.0.1".equals(currentHost) || "0.0.0.0".equals(currentHost)) {
                return true;
            }

            return false;
        }

        /**
         * 获取推荐的 SSL 配置
         */
        public SSLConfig getRecommendedSSLConfig() {
            SSLConfig config = new SSLConfig();
            
            if (shouldAutoGenerateCertificate()) {
                // 自动生成证书配置
                config.setKeyStore(AutoSSLCertificateGenerator.getKeystoreFilePath());
                config.setKeyStorePassword(AutoSSLCertificateGenerator.getKeystorePassword());
                config.setKeyStoreType("PKCS12");
                config.setEnabled(true);
                config.setAutoGenerated(true);
                
                log.info("🔒 推荐使用自动生成的 SSL 证书");
            } else {
                // 使用现有配置或禁用 SSL
                config.setEnabled(false);
                config.setAutoGenerated(false);
                
                log.info("🔒 推荐使用现有 SSL 配置或禁用 SSL");
            }
            
            return config;
        }
    }

    /**
     * SSL 配置信息
     */
    public static class SSLConfig {
        private boolean enabled;
        private String keyStore;
        private String keyStorePassword;
        private String keyStoreType;
        private boolean autoGenerated;

        // Getters and Setters
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }
        
        public String getKeyStore() { return keyStore; }
        public void setKeyStore(String keyStore) { this.keyStore = keyStore; }
        
        public String getKeyStorePassword() { return keyStorePassword; }
        public void setKeyStorePassword(String keyStorePassword) { this.keyStorePassword = keyStorePassword; }
        
        public String getKeyStoreType() { return keyStoreType; }
        public void setKeyStoreType(String keyStoreType) { this.keyStoreType = keyStoreType; }
        
        public boolean isAutoGenerated() { return autoGenerated; }
        public void setAutoGenerated(boolean autoGenerated) { this.autoGenerated = autoGenerated; }
    }

    /**
     * 自动 SSL 属性配置
     */
    public static class AutoSSLProperties {
        
        /**
         * 是否启用自动生成
         */
        private boolean autoGenerate = true;
        
        /**
         * 证书有效期（天）
         */
        private int validityDays = 365;
        
        /**
         * 证书主题信息
         */
        private String subject = "CN=localhost, OU=OTO-System, O=OTO, L=Shanghai, ST=Shanghai, C=CN";
        
        /**
         * SAN (Subject Alternative Names)
         */
        private String[] subjectAlternativeNames = {
            "DNS:localhost",
            "IP:127.0.0.1",
            "IP:0.0.0.0"
        };
        
        /**
         * KeyStore 文件路径
         */
        private String keystorePath;
        
        /**
         * KeyStore 密码
         */
        private String keystorePassword = "oto-ssl-2025";
        
        /**
         * 证书别名
         */
        private String certificateAlias = "oto-service";
        
        /**
         * 密钥算法
         */
        private String keyAlgorithm = "RSA";
        
        /**
         * 密钥长度
         */
        private int keySize = 2048;

        // Getters and Setters
        public boolean isAutoGenerate() { return autoGenerate; }
        public void setAutoGenerate(boolean autoGenerate) { this.autoGenerate = autoGenerate; }
        
        public int getValidityDays() { return validityDays; }
        public void setValidityDays(int validityDays) { this.validityDays = validityDays; }
        
        public String getSubject() { return subject; }
        public void setSubject(String subject) { this.subject = subject; }
        
        public String[] getSubjectAlternativeNames() { return subjectAlternativeNames; }
        public void setSubjectAlternativeNames(String[] subjectAlternativeNames) { 
            this.subjectAlternativeNames = subjectAlternativeNames; 
        }
        
        public String getKeystorePath() { return keystorePath; }
        public void setKeystorePath(String keystorePath) { this.keystorePath = keystorePath; }
        
        public String getKeystorePassword() { return keystorePassword; }
        public void setKeystorePassword(String keystorePassword) { this.keystorePassword = keystorePassword; }
        
        public String getCertificateAlias() { return certificateAlias; }
        public void setCertificateAlias(String certificateAlias) { this.certificateAlias = certificateAlias; }
        
        public String getKeyAlgorithm() { return keyAlgorithm; }
        public void setKeyAlgorithm(String keyAlgorithm) { this.keyAlgorithm = keyAlgorithm; }
        
        public int getKeySize() { return keySize; }
        public void setKeySize(int keySize) { this.keySize = keySize; }
    }
}
