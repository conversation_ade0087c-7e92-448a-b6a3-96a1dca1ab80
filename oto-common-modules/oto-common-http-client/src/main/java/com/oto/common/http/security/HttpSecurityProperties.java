package com.oto.common.http.security;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * HTTP 安全配置属性（公共模块）
 * 定义 FeignClient 和 HTTP 调用相关的安全配置项
 * 
 * 使用方式：在 application.yml 中配置：
 * oto.common.http.security:
 *   enabled: true
 *   feign-client:
 *     direct-access: false
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@Data
@ConfigurationProperties(prefix = "oto.common.http.security")
public class HttpSecurityProperties {

    /**
     * 是否启用 HTTP 安全检查
     */
    private boolean enabled = true;

    /**
     * FeignClient 安全配置
     */
    private FeignClientSecurity feignClient = new FeignClientSecurity();

    /**
     * 监控配置
     */
    private Monitoring monitoring = new Monitoring();

    /**
     * 审计配置
     */
    private Audit audit = new Audit();

    /**
     * FeignClient 安全配置
     */
    @Data
    public static class FeignClientSecurity {
        
        /**
         * 是否启用 FeignClient 安全检查
         */
        private boolean enabled = true;
        
        /**
         * 是否禁止直接访问 FeignClient
         */
        private boolean directAccess = false;
        
        /**
         * 是否只允许 Service 层访问
         */
        private boolean onlyServiceLayer = true;
        
        /**
         * 允许的调用者列表
         */
        private List<String> allowedCallers = List.of(
            "Service", 
            "InternalService", 
            "BusinessService"
        );
        
        /**
         * 禁止的调用者列表
         */
        private List<String> forbiddenCallers = List.of(
            "Controller", 
            "RestController", 
            "FeignController"
        );
        
        /**
         * 可疑模式列表
         */
        private List<String> suspiciousPatterns = List.of(
            "feign",
            "FeignController",
            "RemoteController",
            "InternalController"
        );
        
        /**
         * 可疑路径列表
         */
        private List<String> suspiciousPaths = List.of(
            "feign",
            "remote",
            "/internal"
        );
    }

    /**
     * 监控配置
     */
    @Data
    public static class Monitoring {
        
        /**
         * 是否启用监控
         */
        private boolean enabled = true;
        
        /**
         * 监控间隔（毫秒）
         */
        private long interval = 300000; // 5分钟
        
        /**
         * 是否启用健康检查
         */
        private boolean healthCheck = true;
        
        /**
         * 是否启用指标收集
         */
        private boolean metrics = true;
        
        /**
         * 告警阈值
         */
        private AlertThreshold alertThreshold = new AlertThreshold();
    }

    /**
     * 告警阈值配置
     */
    @Data
    public static class AlertThreshold {
        
        /**
         * 安全违规次数阈值
         */
        private int securityViolationCount = 5;
        
        /**
         * 内存使用率阈值
         */
        private double memoryUsageThreshold = 0.9;
        
        /**
         * CPU 使用率阈值
         */
        private double cpuUsageThreshold = 0.8;
    }

    /**
     * 审计配置
     */
    @Data
    public static class Audit {
        
        /**
         * 是否启用审计
         */
        private boolean enabled = true;
        
        /**
         * 审计日志级别
         */
        private String logLevel = "INFO";
        
        /**
         * 是否记录详细信息
         */
        private boolean detailed = false;
        
        /**
         * 审计事件类型
         */
        private List<String> eventTypes = List.of(
            "FEIGN_CLIENT_ACCESS",
            "CONTROLLER_EXPOSURE",
            "SECURITY_VIOLATION"
        );
    }
}
