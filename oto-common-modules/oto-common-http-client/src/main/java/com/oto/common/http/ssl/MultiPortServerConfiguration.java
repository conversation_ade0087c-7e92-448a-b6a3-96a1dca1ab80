package com.oto.common.http.ssl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.Connector;
import org.apache.tomcat.util.net.SSLHostConfig;
import org.apache.tomcat.util.net.SSLHostConfigCertificate;
import org.apache.tomcat.util.net.SSLHostConfigCertificate.Type;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import java.io.File;

/**
 * 多端口服务器配置
 * 支持同时启动HTTP和HTTPS端口
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
@ConditionalOnProperty(name = "oto.common.http.ssl.selective-mode", havingValue = "true", matchIfMissing = false)
public class MultiPortServerConfiguration {

    private final Environment environment;

    /**
     * 配置HTTPS连接器 (使用WebServerFactoryCustomizer避免Bean冲突)
     */
    @Bean
    @ConditionalOnProperty(name = "oto.common.http.ssl.https-port")
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> httpsConnectorCustomizer() {
        return factory -> {
            // 获取HTTPS端口配置
            Integer httpsPort = environment.getProperty("oto.common.http.ssl.https-port", Integer.class);
            if (httpsPort != null) {
                log.info("🔒 配置HTTPS端口: {}", httpsPort);

                // 创建HTTPS连接器
                Connector httpsConnector = createHttpsConnector(httpsPort);
                if (httpsConnector != null) {
                    factory.addAdditionalTomcatConnectors(httpsConnector);
                    log.info("✅ HTTPS连接器创建成功: {}", httpsPort);
                } else {
                    log.warn("⚠️ HTTPS连接器创建失败，将只使用HTTP端口");
                }
            }
        };
    }

    /**
     * 创建HTTPS连接器
     */
    private Connector createHttpsConnector(int port) {
        try {
            Connector connector = new Connector("org.apache.coyote.http11.Http11NioProtocol");
            connector.setScheme("https");
            connector.setPort(port);
            connector.setSecure(true);

            // 基础SSL配置
            connector.setProperty("SSLEnabled", "true");

            // 获取证书配置
            String keystorePath = environment.getProperty("oto.common.http.ssl.keystore-path");
            String keystorePassword = environment.getProperty("oto.common.http.ssl.keystore-password");
            String certificateAlias = environment.getProperty("oto.common.http.ssl.certificate-alias");

            log.info("🔍 SSL配置调试信息:");
            log.info("  - keystorePath: {}", keystorePath);
            log.info("  - keystorePassword: {}", keystorePassword != null ? "***" : "null");
            log.info("  - certificateAlias: {}", certificateAlias);

            if (keystorePath != null && keystorePassword != null) {
                // 检查证书文件是否存在
                File keystoreFile = new File(keystorePath);
                log.info("🔍 证书文件检查: {}", keystoreFile.getAbsolutePath());
                log.info("🔍 证书文件存在: {}", keystoreFile.exists());

                if (!keystoreFile.exists()) {
                    log.warn("⚠️ SSL证书文件不存在: {}", keystorePath);
                    return null;
                }

                // 使用正确的Tomcat SSLHostConfig配置
                SSLHostConfig sslHostConfig = new SSLHostConfig();
                sslHostConfig.setHostName("_default_");
                sslHostConfig.setProtocols("TLSv1.2,TLSv1.3");

                // 创建SSL证书配置
                SSLHostConfigCertificate certificate = new SSLHostConfigCertificate(sslHostConfig, Type.RSA);
                certificate.setCertificateKeystoreFile(keystoreFile.getAbsolutePath());
                certificate.setCertificateKeystorePassword(keystorePassword);
                certificate.setCertificateKeystoreType("PKCS12");
                certificate.setCertificateKeyPassword(keystorePassword);

                if (certificateAlias != null) {
                    certificate.setCertificateKeyAlias(certificateAlias);
                    log.info("🔍 设置证书别名: {}", certificateAlias);
                }

                // 添加证书到SSLHostConfig
                sslHostConfig.addCertificate(certificate);

                // 添加SSLHostConfig到连接器
                connector.addSslHostConfig(sslHostConfig);

                log.info("🔒 SSL证书配置完成: {}", keystoreFile.getAbsolutePath());
                log.info("🔍 SSLHostConfig配置:");
                log.info("  - hostName: _default_");
                log.info("  - protocols: TLSv1.2,TLSv1.3");
                log.info("  - keystoreFile: {}", keystoreFile.getAbsolutePath());
                log.info("  - keystoreType: PKCS12");
                log.info("  - keyAlias: {}", certificateAlias);
            } else {
                log.warn("⚠️ SSL证书配置不完整");
                return null;
            }
            
            return connector;
            
        } catch (Exception e) {
            log.error("❌ 创建HTTPS连接器失败", e);
            return null;
        }
    }
}
