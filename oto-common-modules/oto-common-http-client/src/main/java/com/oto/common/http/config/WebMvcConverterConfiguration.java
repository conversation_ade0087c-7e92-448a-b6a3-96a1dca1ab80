package com.oto.common.http.config;

import com.oto.common.http.converter.StringToMapConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.lang.NonNull;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web转换器配置
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class WebMvcConverterConfiguration implements WebMvcConfigurer {

    private final StringToMapConverter stringToMapConverter;

    @Override
    public void addFormatters(@NonNull FormatterRegistry registry) {
        log.info("🔄 注册String到Map类型转换器");
        registry.addConverter(stringToMapConverter);
    }
}
