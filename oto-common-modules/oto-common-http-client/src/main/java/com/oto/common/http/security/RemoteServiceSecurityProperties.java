package com.oto.common.http.security;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.List;

/**
 * 远程服务安全配置属性
 * 用于配置被 @RemoteServicePort 注解标注的API的安全认证
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Data
@ConfigurationProperties(prefix = "oto.common.http.remote-service-security")
public class RemoteServiceSecurityProperties {

    /**
     * 是否启用远程服务安全认证
     */
    private boolean enabled = true;

    /**
     * IP白名单配置
     */
    private IpWhitelist ipWhitelist = new IpWhitelist();

    /**
     * 请求签名配置
     */
    private Signature signature = new Signature();

    /**
     * 服务间授权配置
     */
    private ServiceAuth serviceAuth = new ServiceAuth();

    /**
     * IP白名单配置
     */
    @Data
    public static class IpWhitelist {
        /**
         * 是否启用IP白名单验证
         */
        private boolean enabled = true;

        /**
         * 允许的IP地址列表
         * 支持精确IP和CIDR网段格式
         */
        private List<String> allowedIPs = new ArrayList<String>() {{
            add("127.0.0.1");
            add("::1");
            add("localhost");
            add("10.0.0.0/8");
            add("**********/12");
            add("***********/16");
        }};

        /**
         * 是否记录IP访问日志
         */
        private boolean logAccess = true;
    }

    /**
     * 请求签名配置
     */
    @Data
    public static class Signature {
        /**
         * 是否启用请求签名验证
         */
        private boolean enabled = true;

        /**
         * 签名算法
         */
        private String algorithm = "HmacSHA256";

        /**
         * 签名密钥
         */
        private String secretKey = "oto-remote-service-secret-2025";

        /**
         * 请求超时时间（秒）
         */
        private int timeout = 300;

        /**
         * 必需的请求头
         */
        private List<String> requiredHeaders = new ArrayList<String>() {{
            add("X-Timestamp");
            add("X-Nonce");
            add("X-Signature");
            add("X-Service-Id");
        }};

        /**
         * 是否启用防重放攻击
         */
        private boolean antiReplay = true;

        /**
         * 是否记录签名验证日志
         */
        private boolean logValidation = true;
    }

    /**
     * 服务间授权配置
     */
    @Data
    public static class ServiceAuth {
        /**
         * 是否启用服务间授权验证
         */
        private boolean enabled = true;

        /**
         * 允许的服务列表
         */
        private List<String> allowedServices = new ArrayList<String>() {{
            add("oto-admin");
            add("oto-front");
            add("oto-member");
        }};

        /**
         * 是否要求Same-Token验证
         */
        private boolean requireSameToken = true;

        /**
         * 是否启用服务权限验证
         */
        private boolean enableServicePermissions = false;

        /**
         * 服务权限映射
         * key: 服务ID, value: 允许访问的API路径列表
         */
        private java.util.Map<String, List<String>> servicePermissions = new java.util.HashMap<>();

        /**
         * 是否记录授权验证日志
         */
        private boolean logAuthorization = true;
    }

    /**
     * 获取完整的配置信息用于日志输出
     */
    public String getConfigSummary() {
        return String.format(
            "RemoteServiceSecurity[enabled=%s, ipWhitelist=%s, signature=%s, serviceAuth=%s]",
            enabled,
            ipWhitelist.enabled,
            signature.enabled,
            serviceAuth.enabled
        );
    }

    /**
     * 验证配置的有效性
     */
    public boolean isValid() {
        if (!enabled) {
            return true;
        }

        // 验证签名配置
        if (signature.enabled) {
            if (signature.secretKey == null || signature.secretKey.trim().isEmpty()) {
                return false;
            }
            if (signature.timeout <= 0) {
                return false;
            }
        }

        // 验证服务授权配置
        if (serviceAuth.enabled) {
            if (serviceAuth.allowedServices == null || serviceAuth.allowedServices.isEmpty()) {
                return false;
            }
        }

        // 验证IP白名单配置
        if (ipWhitelist.enabled) {
            if (ipWhitelist.allowedIPs == null || ipWhitelist.allowedIPs.isEmpty()) {
                return false;
            }
        }

        return true;
    }
}
