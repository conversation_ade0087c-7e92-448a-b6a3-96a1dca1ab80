package com.oto.common.http.config;

import com.oto.common.core.factory.YmlPropertySourceFactory;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.PropertySource;

/**
 * HTTP客户端自动配置
 *
 * <AUTHOR>
 */
@Slf4j
@AutoConfiguration
@PropertySource(value = "classpath:application-http-client.yml", factory = YmlPropertySourceFactory.class)
public class HttpClientAutoConfiguration {

    @PostConstruct
    public void init() {
        log.info("🔧 HTTP客户端自动配置初始化开始...");
        log.info("🔧 加载HTTP客户端配置文件: application-http-client.yml");
        log.info("✅ HTTP客户端自动配置初始化完成");
    }
}
