package com.oto.common.http.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RestController;

/**
 * FeignClient 安全检查器（公共模块）
 * 在 Bean 创建前检查架构合规性
 * 防止 Controller 直接注入 FeignClient
 * 
 * 使用方式：
 * 1. 在 application.yml 中配置：
 *    oto.common.http.security.feign-client.direct-access: false
 * 2. 自动生效，无需额外配置
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@Slf4j
@Component
@ConditionalOnProperty(
    name = "oto.common.http.security.feign-client.direct-access", 
    havingValue = "false", 
    matchIfMissing = true
)
public class FeignClientSecurityChecker implements BeanFactoryPostProcessor {

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
        log.info("🔍 [公共模块] 开始 FeignClient 安全检查...");
        
        try {
            checkFeignClientUsage(beanFactory);
            checkControllerDefinitions(beanFactory);
            checkSuspiciousPatterns(beanFactory);
            
            log.info("✅ [公共模块] FeignClient 安全检查通过");
        } catch (Exception e) {
            log.error("❌ [公共模块] FeignClient 安全检查失败", e);
            throw new IllegalStateException("FeignClient 安全检查失败，应用启动中止", e);
        }
    }

    /**
     * 检查 FeignClient 的使用情况
     */
    private void checkFeignClientUsage(ConfigurableListableBeanFactory beanFactory) {
        log.debug("[公共模块] 检查 FeignClient 使用情况...");
        
        String[] beanNames = beanFactory.getBeanDefinitionNames();
        
        for (String beanName : beanNames) {
            BeanDefinition beanDefinition = beanFactory.getBeanDefinition(beanName);
            String beanClassName = beanDefinition.getBeanClassName();
            
            if (beanClassName != null) {
                try {
                    Class<?> beanClass = Class.forName(beanClassName);
                    checkBeanClassForFeignClientUsage(beanClass, beanName);
                } catch (ClassNotFoundException e) {
                    log.debug("无法加载类: {}", beanClassName);
                }
            }
        }
    }

    /**
     * 检查 Bean 类是否违规使用 FeignClient
     */
    private void checkBeanClassForFeignClientUsage(Class<?> beanClass, String beanName) {
        // 检查是否是 Controller
        if (beanClass.isAnnotationPresent(RestController.class) || 
            beanClass.isAnnotationPresent(Controller.class)) {
            
            // 检查字段中是否有 FeignClient
            java.lang.reflect.Field[] fields = beanClass.getDeclaredFields();
            for (java.lang.reflect.Field field : fields) {
                Class<?> fieldType = field.getType();
                
                if (isFeignClient(fieldType)) {
                    String errorMessage = String.format(
                        "❌ [公共模块] 安全违规：Controller '%s' 直接注入了 FeignClient '%s'。" +
                        "FeignClient 只能在 Service 层使用！请通过 Service 层调用。",
                        beanClass.getSimpleName(),
                        fieldType.getSimpleName()
                    );
                    
                    log.error(errorMessage);
                    throw new SecurityException(errorMessage);
                }
            }
        }
    }

    /**
     * 检查 Controller 定义
     */
    private void checkControllerDefinitions(ConfigurableListableBeanFactory beanFactory) {
        log.debug("[公共模块] 检查 Controller 定义...");
        
        String[] beanNames = beanFactory.getBeanDefinitionNames();
        
        for (String beanName : beanNames) {
            BeanDefinition beanDefinition = beanFactory.getBeanDefinition(beanName);
            
            if (beanDefinition.getBeanClassName() != null) {
                try {
                    Class<?> beanClass = Class.forName(beanDefinition.getBeanClassName());
                    checkControllerMapping(beanClass, beanName);
                } catch (ClassNotFoundException e) {
                    log.debug("无法加载类: {}", beanDefinition.getBeanClassName());
                }
            }
        }
    }

    /**
     * 检查 Controller 映射路径
     */
    private void checkControllerMapping(Class<?> controllerClass, String beanName) {
        if (controllerClass.isAnnotationPresent(RestController.class)) {
            
            // 检查类名是否包含可疑模式
            String className = controllerClass.getSimpleName();
            if (containsSuspiciousPattern(className)) {
                log.warn("⚠️ [公共模块] 检测到可疑的 Controller 类名: {}", className);
            }
            
            // 检查 RequestMapping 注解
            org.springframework.web.bind.annotation.RequestMapping mapping = 
                controllerClass.getAnnotation(org.springframework.web.bind.annotation.RequestMapping.class);
            
            if (mapping != null) {
                String[] paths = mapping.value();
                for (String path : paths) {
                    if (containsSuspiciousPath(path)) {
                        log.warn("⚠️ [公共模块] 检测到可疑的映射路径: Controller '{}', 路径 '{}'", 
                                className, path);
                    }
                }
            }
        }
    }

    /**
     * 检查可疑模式
     */
    private void checkSuspiciousPatterns(ConfigurableListableBeanFactory beanFactory) {
        log.debug("[公共模块] 检查可疑模式...");
        
        String[] beanNames = beanFactory.getBeanDefinitionNames();
        
        for (String beanName : beanNames) {
            // 检查 Bean 名称是否包含可疑模式
            if (containsSuspiciousPattern(beanName)) {
                BeanDefinition beanDefinition = beanFactory.getBeanDefinition(beanName);
                String beanClassName = beanDefinition.getBeanClassName();
                
                if (beanClassName != null) {
                    try {
                        Class<?> beanClass = Class.forName(beanClassName);
                        if (beanClass.isAnnotationPresent(RestController.class)) {
                            log.warn("⚠️ [公共模块] 检测到可疑的 Controller Bean: {}", beanName);
                        }
                    } catch (ClassNotFoundException e) {
                        log.debug("无法加载类: {}", beanClassName);
                    }
                }
            }
        }
    }

    /**
     * 判断是否是 FeignClient
     */
    private boolean isFeignClient(Class<?> clazz) {
        return clazz.isAnnotationPresent(FeignClient.class) ||
               clazz.getSimpleName().contains("FeignClient") ||
               (clazz.getPackage() != null && 
                clazz.getPackage().getName().contains("feign"));
    }

    /**
     * 检查是否包含可疑模式
     */
    private boolean containsSuspiciousPattern(String text) {
        if (text == null) {
            return false;
        }
        
        String[] suspiciousPatterns = {
            "feign",
            "FeignController",
            "RemoteController",
            "InternalController"
        };
        
        String lowerText = text.toLowerCase();
        for (String pattern : suspiciousPatterns) {
            if (lowerText.contains(pattern.toLowerCase())) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查是否包含可疑路径
     */
    private boolean containsSuspiciousPath(String path) {
        if (path == null) {
            return false;
        }
        
        String[] suspiciousPatterns = {
            "feign",
            "remote",
            "/internal"
        };
        
        String lowerPath = path.toLowerCase();
        for (String pattern : suspiciousPatterns) {
            if (lowerPath.contains(pattern)) {
                return true;
            }
        }
        
        return false;
    }
}
