package com.oto.common.http.security;

import com.oto.common.http.annotation.RemoteServicePort;
import com.oto.common.http.security.validator.IPWhitelistValidator;
import com.oto.common.http.security.validator.ServiceAuthValidator;
import com.oto.common.http.security.validator.SignatureValidator;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;

/**
 * 远程服务安全拦截器
 * 对标注了 @RemoteServicePort 的API进行安全验证
 * 包括IP白名单、请求签名、服务间授权等多重验证
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RemoteServiceSecurityInterceptor implements HandlerInterceptor {

    private final RemoteServiceSecurityProperties securityProperties;
    private final IPWhitelistValidator ipValidator;
    private final SignatureValidator signatureValidator;
    private final ServiceAuthValidator serviceAuthValidator;

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler)
            throws Exception {

        // 1. 检查是否是Controller方法
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Class<?> controllerClass = handlerMethod.getBeanType();

        // 2. 检查Controller是否标注了@RemoteServicePort
        if (!controllerClass.isAnnotationPresent(RemoteServicePort.class)) {
            // 普通API，直接通过
            log.debug("✅ 普通API访问: {}", request.getRequestURI());
            return true;
        }

        // 3. 远程服务API，记录用户信息（用户信息将由MyBatis注入处理器直接从HTTP头获取）
        String userId = request.getHeader("X-User-Id");
        String deptId = request.getHeader("X-User-Dept-Id");
        if (userId != null) {
            log.debug("🔧 远程服务API检测到用户信息: userId={}, deptId={}", userId, deptId);
        }

        // 3. 检查安全功能是否启用
        if (!securityProperties.isEnabled()) {
            log.debug("✅ 远程服务API访问: {} (安全验证已禁用)", request.getRequestURI());
            return true;
        }

        // 4. 远程服务API，进行安全验证
        String serviceId = request.getHeader("X-Service-Id");
        String clientIP = getClientIP(request);
        
        log.info("🔍 远程服务API安全验证开始: uri={}, serviceId={}, clientIP={}", 
                request.getRequestURI(), serviceId, clientIP);

        // 5. IP白名单验证
        if (!ipValidator.validate(request)) {
            return rejectRequest(response, "IP_NOT_ALLOWED", 
                "IP地址不在白名单中", request.getRequestURI(), clientIP);
        }

        // 6. 请求签名验证
        if (!signatureValidator.validate(request)) {
            return rejectRequest(response, "SIGNATURE_INVALID", 
                "请求签名验证失败", request.getRequestURI(), serviceId);
        }

        // 7. 服务间授权验证
        if (!serviceAuthValidator.validate(request)) {
            return rejectRequest(response, "SERVICE_AUTH_FAILED", 
                "服务授权验证失败", request.getRequestURI(), serviceId);
        }

        // 8. 标记为已验证的远程请求
        markAsVerifiedRemoteRequest(request, serviceId, clientIP);

        log.info("✅ 远程服务API安全验证通过: uri={}, serviceId={}, clientIP={}", 
                request.getRequestURI(), serviceId, clientIP);

        return true;
    }

    @Override
    public void afterCompletion(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response,
                              @NonNull Object handler, @Nullable Exception ex) throws Exception {

        // 记录远程服务访问完成情况
        if (isVerifiedRemoteRequest(request)) {
            String serviceId = (String) request.getAttribute("REMOTE_SERVICE_ID");
            int status = response.getStatus();

            if (ex != null) {
                log.warn("⚠️ 远程服务API执行异常: uri={}, serviceId={}, status={}, error={}",
                        request.getRequestURI(), serviceId, status, ex.getMessage());
            } else {
                log.debug("✅ 远程服务API执行完成: uri={}, serviceId={}, status={}",
                         request.getRequestURI(), serviceId, status);
            }
        }
    }

    /**
     * 拒绝请求并返回错误响应
     */
    private boolean rejectRequest(HttpServletResponse response, String errorCode, String message, 
                                String uri, String identifier) throws IOException {
        
        response.setStatus(HttpServletResponse.SC_FORBIDDEN);
        response.setContentType("application/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");

        // 构建错误响应
        String jsonResponse = buildErrorResponse(errorCode, message);
        response.getWriter().write(jsonResponse);

        log.warn("❌ 远程服务访问被拒绝: uri={}, errorCode={}, message={}, identifier={}", 
                uri, errorCode, message, identifier);
        
        return false;
    }

    /**
     * 构建错误响应JSON
     */
    private String buildErrorResponse(String errorCode, String message) {
        return String.format(
            "{\"success\":false,\"code\":\"%s\",\"message\":\"%s\",\"timestamp\":%d,\"data\":null}",
            errorCode, message, System.currentTimeMillis()
        );
    }

    /**
     * 标记为已验证的远程请求
     */
    private void markAsVerifiedRemoteRequest(HttpServletRequest request, String serviceId, String clientIP) {
        request.setAttribute("REMOTE_SERVICE_VERIFIED", true);
        request.setAttribute("REMOTE_SERVICE_ID", serviceId);
        request.setAttribute("REMOTE_CLIENT_IP", clientIP);
        request.setAttribute("REMOTE_VERIFICATION_TIME", System.currentTimeMillis());
    }

    /**
     * 检查是否是已验证的远程请求
     */
    private boolean isVerifiedRemoteRequest(HttpServletRequest request) {
        return Boolean.TRUE.equals(request.getAttribute("REMOTE_SERVICE_VERIFIED"));
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIP(HttpServletRequest request) {
        String[] headers = {
            "X-Forwarded-For",
            "X-Real-IP",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP"
        };

        for (String header : headers) {
            String ip = request.getHeader(header);
            if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
                return ip.split(",")[0].trim();
            }
        }

        return request.getRemoteAddr();
    }

    /**
     * 获取拦截器统计信息
     */
    public String getInterceptorStats() {
        return String.format(
            "RemoteServiceSecurityInterceptor[enabled=%s, %s, %s, %s]",
            securityProperties.isEnabled(),
            ipValidator.getValidationStats(),
            signatureValidator.getValidationStats(),
            serviceAuthValidator.getAuthStats()
        );
    }

    /**
     * 检查Controller是否是远程服务
     */
    public boolean isRemoteServiceController(Class<?> controllerClass) {
        return controllerClass.isAnnotationPresent(RemoteServicePort.class);
    }

    /**
     * 获取安全配置摘要
     */
    public String getSecurityConfigSummary() {
        return securityProperties.getConfigSummary();
    }

    /**
     * 验证配置有效性
     */
    public boolean isConfigurationValid() {
        return securityProperties.isValid();
    }
}
