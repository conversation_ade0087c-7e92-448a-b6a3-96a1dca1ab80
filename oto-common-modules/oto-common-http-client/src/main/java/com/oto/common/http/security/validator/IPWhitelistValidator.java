package com.oto.common.http.security.validator;

import com.oto.common.http.security.RemoteServiceSecurityProperties;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.List;

/**
 * IP白名单验证器
 * 验证请求来源IP是否在白名单中
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class IPWhitelistValidator {

    private final RemoteServiceSecurityProperties properties;

    /**
     * 验证IP白名单
     *
     * @param request HTTP请求
     * @return 验证结果
     */
    public boolean validate(HttpServletRequest request) {
        RemoteServiceSecurityProperties.IpWhitelist config = properties.getIpWhitelist();
        
        if (!config.isEnabled()) {
            if (config.isLogAccess()) {
                log.debug("✅ IP白名单验证跳过: 功能已禁用");
            }
            return true;
        }

        try {
            String clientIP = getClientIP(request);
            List<String> whitelist = config.getAllowedIPs();

            if (config.isLogAccess()) {
                log.debug("🔍 IP白名单验证: 客户端IP={}, 白名单数量={}", clientIP, whitelist.size());
            }

            // 精确匹配
            if (whitelist.contains(clientIP)) {
                if (config.isLogAccess()) {
                    log.debug("✅ IP白名单验证成功 (精确匹配): {}", clientIP);
                }
                return true;
            }

            // 特殊处理localhost
            if (isLocalhost(clientIP) && containsLocalhost(whitelist)) {
                if (config.isLogAccess()) {
                    log.debug("✅ IP白名单验证成功 (localhost): {}", clientIP);
                }
                return true;
            }

            // CIDR网段匹配
            for (String allowedIP : whitelist) {
                if (allowedIP.contains("/")) {
                    if (isIPInCIDR(clientIP, allowedIP)) {
                        if (config.isLogAccess()) {
                            log.debug("✅ IP白名单验证成功 (CIDR): {} in {}", clientIP, allowedIP);
                        }
                        return true;
                    }
                }
            }

            log.warn("❌ IP白名单验证失败: {} 不在白名单中", clientIP);
            return false;

        } catch (Exception e) {
            log.error("❌ IP白名单验证异常", e);
            return false;
        }
    }

    /**
     * 获取客户端真实IP地址
     * 处理代理和负载均衡的情况
     */
    private String getClientIP(HttpServletRequest request) {
        // 常见的代理头部，按优先级排序
        String[] headers = {
            "X-Forwarded-For",
            "X-Real-IP",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR"
        };

        for (String header : headers) {
            String ip = request.getHeader(header);
            if (StringUtils.hasText(ip) && !"unknown".equalsIgnoreCase(ip)) {
                // X-Forwarded-For可能包含多个IP，取第一个
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                if (isValidIP(ip)) {
                    return ip;
                }
            }
        }

        // 如果没有代理头部，使用直连IP
        return request.getRemoteAddr();
    }

    /**
     * 检查IP是否在CIDR网段中
     */
    private boolean isIPInCIDR(String ip, String cidr) {
        try {
            String[] parts = cidr.split("/");
            if (parts.length != 2) {
                return false;
            }

            InetAddress targetAddr = InetAddress.getByName(ip);
            InetAddress networkAddr = InetAddress.getByName(parts[0]);
            int prefixLength = Integer.parseInt(parts[1]);

            byte[] targetBytes = targetAddr.getAddress();
            byte[] networkBytes = networkAddr.getAddress();

            if (targetBytes.length != networkBytes.length) {
                return false; // IPv4 vs IPv6
            }

            int bytesToCheck = prefixLength / 8;
            int bitsToCheck = prefixLength % 8;

            // 检查完整字节
            for (int i = 0; i < bytesToCheck; i++) {
                if (targetBytes[i] != networkBytes[i]) {
                    return false;
                }
            }

            // 检查部分字节
            if (bitsToCheck > 0 && bytesToCheck < targetBytes.length) {
                int mask = 0xFF << (8 - bitsToCheck);
                return (targetBytes[bytesToCheck] & mask) == (networkBytes[bytesToCheck] & mask);
            }

            return true;

        } catch (Exception e) {
            log.warn("CIDR匹配异常: ip={}, cidr={}, error={}", ip, cidr, e.getMessage());
            return false;
        }
    }

    /**
     * 检查是否是localhost
     */
    private boolean isLocalhost(String ip) {
        return "127.0.0.1".equals(ip) || 
               "::1".equals(ip) || 
               "0:0:0:0:0:0:0:1".equals(ip) ||
               "localhost".equalsIgnoreCase(ip);
    }

    /**
     * 检查白名单是否包含localhost相关配置
     */
    private boolean containsLocalhost(List<String> whitelist) {
        return whitelist.stream().anyMatch(ip -> 
            "127.0.0.1".equals(ip) || 
            "::1".equals(ip) || 
            "localhost".equalsIgnoreCase(ip) ||
            "*********/8".equals(ip)
        );
    }

    /**
     * 验证IP地址格式是否有效
     */
    private boolean isValidIP(String ip) {
        try {
            InetAddress.getByName(ip);
            return true;
        } catch (UnknownHostException e) {
            return false;
        }
    }

    /**
     * 获取IP验证统计信息
     */
    public String getValidationStats() {
        RemoteServiceSecurityProperties.IpWhitelist config = properties.getIpWhitelist();
        return String.format(
            "IPWhitelist[enabled=%s, allowedIPs=%d, logAccess=%s]",
            config.isEnabled(),
            config.getAllowedIPs().size(),
            config.isLogAccess()
        );
    }
}
