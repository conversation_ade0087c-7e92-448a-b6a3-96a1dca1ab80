package com.oto.common.http.security;

import com.oto.common.http.security.validator.IPWhitelistValidator;
import com.oto.common.http.security.validator.ServiceAuthValidator;
import com.oto.common.http.security.validator.SignatureValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.NonNull;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 远程服务安全配置
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(RemoteServiceSecurityProperties.class)
public class RemoteServiceSecurityConfiguration implements WebMvcConfigurer {

    private final RemoteServiceSecurityProperties properties;

    public RemoteServiceSecurityConfiguration(RemoteServiceSecurityProperties properties) {
        this.properties = properties;
    }

    // 验证器Bean由@Component注解自动创建，这里不需要重复定义

    /**
     * 远程服务安全拦截器
     */
    @Bean
    @ConditionalOnProperty(name = "oto.common.http.remote-service-security.enabled", havingValue = "true", matchIfMissing = true)
    public RemoteServiceSecurityInterceptor remoteServiceSecurityInterceptor(
            IPWhitelistValidator ipValidator,
            SignatureValidator signatureValidator,
            ServiceAuthValidator serviceAuthValidator) {
        return new RemoteServiceSecurityInterceptor(
                properties, ipValidator, signatureValidator, serviceAuthValidator);
    }

    @Override
    public void addInterceptors(@NonNull InterceptorRegistry registry) {
        // 拦截器注册由Spring自动处理，这里不需要手动注册
        // RemoteServiceSecurityInterceptor 会通过 @Bean 自动注册
    }
}
