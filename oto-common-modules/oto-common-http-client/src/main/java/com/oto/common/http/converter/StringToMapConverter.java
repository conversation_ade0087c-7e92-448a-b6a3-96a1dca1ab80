package com.oto.common.http.converter;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * String到Map的类型转换器
 * 用于处理URL参数中的JSON字符串转换为Map对象
 * 
 * <AUTHOR>
 * @date 2025-09-01
 */
@Slf4j
@Component
public class StringToMapConverter implements Converter<String, Map<String, Object>> {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public Map<String, Object> convert(String source) {
        if (source == null || source.trim().isEmpty()) {
            log.debug("🔄 转换空字符串为空Map");
            return new HashMap<>();
        }

        try {
            // 尝试解析JSON字符串
            if (source.trim().startsWith("{") && source.trim().endsWith("}")) {
                Map<String, Object> result = objectMapper.readValue(source, new TypeReference<Map<String, Object>>() {});
                log.debug("🔄 成功转换JSON字符串为Map: {} -> {}", source, result);
                return result;
            }
            
            // 如果不是JSON格式，创建包含原始字符串的Map
            Map<String, Object> result = new HashMap<>();
            result.put("value", source);
            log.debug("🔄 转换普通字符串为Map: {} -> {}", source, result);
            return result;
            
        } catch (Exception e) {
            log.warn("⚠️ 转换字符串为Map失败: {}, 错误: {}", source, e.getMessage());
            // 转换失败时返回包含原始字符串的Map
            Map<String, Object> result = new HashMap<>();
            result.put("value", source);
            result.put("error", "JSON解析失败");
            return result;
        }
    }
}
