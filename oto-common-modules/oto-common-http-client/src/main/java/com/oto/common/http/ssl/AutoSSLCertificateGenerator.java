package com.oto.common.http.ssl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.KeyStore;
import java.security.cert.X509Certificate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * 自动 SSL 证书生成器
 * 用于同机器部署时自动生成自签名证书
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@Slf4j
@Component
public class AutoSSLCertificateGenerator {

    private static final String KEYSTORE_PATH = "ssl/auto-generated.p12";
    private static final String KEYSTORE_PASSWORD = "oto-ssl-2025";
    private static final String CERTIFICATE_ALIAS = "oto-service";

    /**
     * 应用启动后自动检查并生成证书
     */
    @EventListener(ApplicationReadyEvent.class)
    public void generateCertificateIfNeeded() {
        try {
            String keystorePath = getKeystorePath();
            
            if (!certificateExists(keystorePath)) {
                log.info("🔒 未找到 SSL 证书，开始自动生成...");
                generateSelfSignedCertificate(keystorePath);
                log.info("✅ SSL 证书生成完成: {}", keystorePath);
            } else {
                log.info("🔒 SSL 证书已存在: {}", keystorePath);
                checkCertificateExpiry(keystorePath);
            }
            
        } catch (Exception e) {
            log.error("❌ SSL 证书生成失败", e);
        }
    }

    /**
     * 生成自签名证书
     */
    private void generateSelfSignedCertificate(String keystorePath) throws Exception {
        // 1. 生成密钥对
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        keyPairGenerator.initialize(2048);
        KeyPair keyPair = keyPairGenerator.generateKeyPair();

        // 2. 创建证书
        X509Certificate certificate = createSelfSignedCertificate(keyPair);

        // 3. 创建 KeyStore
        KeyStore keyStore = KeyStore.getInstance("PKCS12");
        keyStore.load(null, null);
        
        // 4. 添加证书和私钥到 KeyStore
        keyStore.setKeyEntry(
            CERTIFICATE_ALIAS,
            keyPair.getPrivate(),
            KEYSTORE_PASSWORD.toCharArray(),
            new X509Certificate[]{certificate}
        );

        // 5. 保存 KeyStore 到文件
        saveKeyStore(keyStore, keystorePath);
        
        log.info("🔑 生成的证书信息:");
        log.info("  - 主题: {}", certificate.getSubjectDN());
        log.info("  - 有效期: {} 到 {}", certificate.getNotBefore(), certificate.getNotAfter());
        log.info("  - 序列号: {}", certificate.getSerialNumber());
    }

    /**
     * 创建自签名证书
     */
    private X509Certificate createSelfSignedCertificate(KeyPair keyPair) throws Exception {
        // 使用 Bouncy Castle 或 Java 内置 API 创建证书
        // 这里简化实现，实际项目中建议使用专业的证书生成库
        
        // 证书有效期：1年
        Date notBefore = new Date();
        Date notAfter = Date.from(
            LocalDateTime.now().plusYears(1).atZone(ZoneId.systemDefault()).toInstant()
        );

        // 证书主题信息
        String subject = "CN=localhost, OU=OTO-System, O=OTO, L=Shanghai, ST=Shanghai, C=CN";
        
        // 这里需要使用证书生成库，如 Bouncy Castle
        // 为了简化，这里返回一个模拟的证书生成过程
        return generateCertificateWithBouncyCastle(keyPair, subject, notBefore, notAfter);
    }

    /**
     * 使用 Bouncy Castle 生成证书
     */
    private X509Certificate generateCertificateWithBouncyCastle(
            KeyPair keyPair, String subject, Date notBefore, Date notAfter) throws Exception {
        
        // 注意：这里需要添加 Bouncy Castle 依赖
        // 实际实现需要使用 X509V3CertificateGenerator 或类似的 API
        
        // 临时实现：使用 keytool 命令生成
        return generateCertificateWithKeytool(keyPair, subject, notBefore, notAfter);
    }

    /**
     * 使用 keytool 命令生成证书（临时方案）
     */
    private X509Certificate generateCertificateWithKeytool(
            KeyPair keyPair, String subject, Date notBefore, Date notAfter) throws Exception {
        
        // 构建 keytool 命令
        String keystorePath = getKeystorePath();
        String[] command = {
            "keytool",
            "-genkeypair",
            "-alias", CERTIFICATE_ALIAS,
            "-keyalg", "RSA",
            "-keysize", "2048",
            "-storetype", "PKCS12",
            "-keystore", keystorePath,
            "-storepass", KEYSTORE_PASSWORD,
            "-keypass", KEYSTORE_PASSWORD,
            "-validity", "365",
            "-dname", "CN=localhost, OU=OTO-System, O=OTO, L=Shanghai, ST=Shanghai, C=CN",
            "-ext", "SAN=DNS:localhost,IP:127.0.0.1,IP:0.0.0.0"
        };

        // 执行命令
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        Process process = processBuilder.start();
        int exitCode = process.waitFor();

        if (exitCode != 0) {
            throw new RuntimeException("keytool 命令执行失败，退出码: " + exitCode);
        }

        // 加载生成的证书
        return loadCertificateFromKeystore(keystorePath);
    }

    /**
     * 从 KeyStore 加载证书
     */
    private X509Certificate loadCertificateFromKeystore(String keystorePath) throws Exception {
        KeyStore keyStore = KeyStore.getInstance("PKCS12");
        try (var fis = Files.newInputStream(Paths.get(keystorePath))) {
            keyStore.load(fis, KEYSTORE_PASSWORD.toCharArray());
        }
        return (X509Certificate) keyStore.getCertificate(CERTIFICATE_ALIAS);
    }

    /**
     * 保存 KeyStore 到文件
     */
    private void saveKeyStore(KeyStore keyStore, String keystorePath) throws Exception {
        // 确保目录存在
        Path path = Paths.get(keystorePath);
        Files.createDirectories(path.getParent());

        // 保存 KeyStore
        try (FileOutputStream fos = new FileOutputStream(keystorePath)) {
            keyStore.store(fos, KEYSTORE_PASSWORD.toCharArray());
        }
    }

    /**
     * 检查证书是否存在
     */
    private boolean certificateExists(String keystorePath) {
        return Files.exists(Paths.get(keystorePath));
    }

    /**
     * 检查证书过期时间
     */
    private void checkCertificateExpiry(String keystorePath) {
        try {
            X509Certificate certificate = loadCertificateFromKeystore(keystorePath);
            Date notAfter = certificate.getNotAfter();
            Date now = new Date();
            
            long daysUntilExpiry = (notAfter.getTime() - now.getTime()) / (1000 * 60 * 60 * 24);
            
            if (daysUntilExpiry < 30) {
                log.warn("⚠️ SSL 证书将在 {} 天后过期，建议更新证书", daysUntilExpiry);
            } else {
                log.info("🔒 SSL 证书有效，还有 {} 天过期", daysUntilExpiry);
            }
            
        } catch (Exception e) {
            log.error("检查证书过期时间失败", e);
        }
    }

    /**
     * 获取 KeyStore 文件路径
     */
    private String getKeystorePath() {
        // 优先使用系统属性指定的路径
        String customPath = System.getProperty("oto.ssl.keystore.path");
        if (customPath != null) {
            return customPath;
        }

        // 使用默认路径
        String userHome = System.getProperty("user.home");
        return userHome + File.separator + ".oto" + File.separator + KEYSTORE_PATH;
    }

    /**
     * 获取 KeyStore 密码
     */
    public static String getKeystorePassword() {
        return System.getProperty("oto.ssl.keystore.password", KEYSTORE_PASSWORD);
    }

    /**
     * 获取 KeyStore 文件路径（静态方法）
     */
    public static String getKeystoreFilePath() {
        String customPath = System.getProperty("oto.ssl.keystore.path");
        if (customPath != null) {
            return customPath;
        }

        String userHome = System.getProperty("user.home");
        return userHome + File.separator + ".oto" + File.separator + KEYSTORE_PATH;
    }
}
