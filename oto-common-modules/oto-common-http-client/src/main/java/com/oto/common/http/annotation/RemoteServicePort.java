package com.oto.common.http.annotation;

import java.lang.annotation.*;

/**
 * 远程服务端口注解
 * 用于标记 Controller 只能在指定端口访问
 *
 * 使用场景：
 * - 将某些 Controller 暴露在特定端口供 OpenFeign 调用
 * - 实现 Controller 级别的端口隔离
 * - 主端口无法访问标记的 Controller
 *
 * 使用示例：
 * @RestController
 * @RemoteServicePort  // 默认使用主端口+10的偏移端口
 * public class TestController {
 *     // 该 Controller 只能通过偏移端口访问（如 8080+10=8090）
 *     // 8080 主端口无法访问
 * }
 *
 * @RestController
 * @RemoteServicePort(8091)  // 指定特定端口
 * public class CustomController {
 *     // 该 Controller 只能通过 8091 端口访问
 * }
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RemoteServicePort {

    /**
     * 指定该 Controller 只能访问的端口
     * 默认值 -1 表示使用主端口+偏移量的计算端口
     *
     * @return 端口号，-1 表示使用默认计算端口
     */
    int value() default -1;

    /**
     * 描述信息
     *
     * @return 描述
     */
    String description() default "远程服务端口";
}
