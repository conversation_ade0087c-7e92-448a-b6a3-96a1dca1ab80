package com.oto.common.http.security;

import feign.RequestInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 远程服务Feign配置
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(RemoteServiceSecurityProperties.class)
public class RemoteServiceFeignConfiguration {

    /**
     * 远程服务Feign拦截器
     */
    @Bean
    @ConditionalOnClass(RequestInterceptor.class)
    @ConditionalOnProperty(name = "oto.common.http.remote-service-security.enabled", havingValue = "true", matchIfMissing = true)
    public RemoteServiceFeignInterceptor remoteServiceFeignInterceptor(RemoteServiceSecurityProperties properties) {
        log.info("🔍 创建远程服务Feign拦截器...");
        RemoteServiceFeignInterceptor interceptor = new RemoteServiceFeignInterceptor(properties);
        log.info("✅ 远程服务Feign拦截器创建完成: {}", interceptor.getInterceptorStats());
        return interceptor;
    }
}
