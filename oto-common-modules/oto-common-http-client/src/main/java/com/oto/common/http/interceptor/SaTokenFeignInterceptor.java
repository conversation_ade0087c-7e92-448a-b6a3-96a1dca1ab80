package com.oto.common.http.interceptor;

import cn.dev33.satoken.same.SaSameUtil;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.StrUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;

/**
 * Sa-Token OpenFeign 请求拦截器
 * 自动为 OpenFeign 请求添加 Same-Token 和用户认证信息
 *
 * <AUTHOR>
 * @date 2025-08-30
 */
@Slf4j
public class SaTokenFeignInterceptor implements RequestInterceptor {
    
    @Override
    public void apply(RequestTemplate template) {
        try {
            // 1. 为 Feign 请求追加 Same-Token (用于服务间认证)
            String sameToken = SaSameUtil.getToken();
            if (StrUtil.isNotBlank(sameToken)) {
                template.header(SaSameUtil.SAME_TOKEN, sameToken);
                log.debug("为 Feign 请求添加 Same-Token: {}", template.url());
            }
            
            // 2. 传递用户认证信息 (如果存在)
            try {
                String userToken = StpUtil.getTokenValue();
                if (StrUtil.isNotBlank(userToken)) {
                    // 添加用户部门信息替代Authorization token
                    Object deptId = StpUtil.getSession().get("deptId");
                    if (deptId != null) {
                        template.header("X-User-Dept-Id", String.valueOf(deptId));
                    }

                    template.header("X-User-Id", StpUtil.getLoginIdAsString());
                    template.header("X-User-Type", StpUtil.getLoginType());
                    log.debug("为 Feign 请求添加用户认证信息，用户ID: {}, 部门ID: {}",
                             StpUtil.getLoginIdAsString(), deptId);
                }
            } catch (Exception e) {
                // 用户未登录时忽略，只进行服务间认证
                log.debug("当前无用户登录状态，仅进行服务间认证");
            }
            
            // 3. 添加请求标识
            template.header("X-Request-Source", "FEIGN_CLIENT");
            template.header("X-Request-Time", String.valueOf(System.currentTimeMillis()));
            
        } catch (Exception e) {
            log.error("Feign 请求拦截器处理失败: {}", e.getMessage(), e);
        }
    }
}
