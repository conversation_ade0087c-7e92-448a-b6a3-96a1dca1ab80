package com.oto.common.http.security.validator;

import cn.dev33.satoken.same.SaSameUtil;
import com.oto.common.http.security.RemoteServiceSecurityProperties;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 服务间授权验证器
 * 验证服务间的调用授权，包括服务白名单和Same-Token验证
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ServiceAuthValidator {

    private final RemoteServiceSecurityProperties properties;

    /**
     * 验证服务间授权
     *
     * @param request HTTP请求
     * @return 验证结果
     */
    public boolean validate(HttpServletRequest request) {
        RemoteServiceSecurityProperties.ServiceAuth config = properties.getServiceAuth();
        
        if (!config.isEnabled()) {
            if (config.isLogAuthorization()) {
                log.debug("✅ 服务授权验证跳过: 功能已禁用");
            }
            return true;
        }

        try {
            // 1. 获取服务ID
            String serviceId = request.getHeader("X-Service-Id");
            if (!StringUtils.hasText(serviceId)) {
                log.warn("❌ 服务授权失败: 缺少服务ID");
                return false;
            }

            if (config.isLogAuthorization()) {
                log.debug("🔍 服务授权验证开始: serviceId={}", serviceId);
            }

            // 2. 检查服务白名单
            if (!isServiceAllowed(serviceId)) {
                log.warn("❌ 服务授权失败: 服务不在白名单中: {}", serviceId);
                return false;
            }

            // 3. 验证Same-Token (Sa-Token服务间认证)
            if (config.isRequireSameToken()) {
                if (!validateSameToken(request, serviceId)) {
                    return false;
                }
            }

            // 4. 验证服务特定权限
            if (config.isEnableServicePermissions()) {
                if (!validateServicePermissions(serviceId, request)) {
                    return false;
                }
            }

            if (config.isLogAuthorization()) {
                log.debug("✅ 服务授权验证成功: serviceId={}", serviceId);
            }
            return true;

        } catch (Exception e) {
            log.error("❌ 服务授权验证异常", e);
            return false;
        }
    }

    /**
     * 检查服务是否在白名单中
     */
    private boolean isServiceAllowed(String serviceId) {
        List<String> allowedServices = properties.getServiceAuth().getAllowedServices();
        return allowedServices.contains(serviceId);
    }

    /**
     * 验证Same-Token
     */
    private boolean validateSameToken(HttpServletRequest request, String serviceId) {
        try {
            String sameToken = request.getHeader(SaSameUtil.SAME_TOKEN);
            if (!StringUtils.hasText(sameToken)) {
                log.warn("❌ Same-Token验证失败: 缺少Same-Token头部, serviceId={}", serviceId);
                return false;
            }

            // 验证Same-Token有效性
            SaSameUtil.checkToken(sameToken);
            
            if (properties.getServiceAuth().isLogAuthorization()) {
                log.debug("✅ Same-Token验证成功: serviceId={}", serviceId);
            }
            return true;

        } catch (Exception e) {
            log.warn("❌ Same-Token验证失败: serviceId={}, error={}", serviceId, e.getMessage());
            return false;
        }
    }

    /**
     * 验证服务特定权限
     */
    private boolean validateServicePermissions(String serviceId, HttpServletRequest request) {
        try {
            Map<String, List<String>> servicePermissions = properties.getServiceAuth().getServicePermissions();
            
            // 如果没有配置特定权限，则允许访问
            if (servicePermissions == null || servicePermissions.isEmpty()) {
                return true;
            }

            List<String> allowedPaths = servicePermissions.get(serviceId);
            if (allowedPaths == null || allowedPaths.isEmpty()) {
                // 如果服务没有配置特定权限，则允许访问所有API
                return true;
            }

            String requestURI = request.getRequestURI();
            
            // 检查请求路径是否在允许的路径列表中
            for (String allowedPath : allowedPaths) {
                if (isPathMatched(requestURI, allowedPath)) {
                    if (properties.getServiceAuth().isLogAuthorization()) {
                        log.debug("✅ 服务权限验证成功: serviceId={}, path={}, allowedPath={}", 
                                 serviceId, requestURI, allowedPath);
                    }
                    return true;
                }
            }

            log.warn("❌ 服务权限验证失败: serviceId={}, path={}, allowedPaths={}", 
                     serviceId, requestURI, allowedPaths);
            return false;

        } catch (Exception e) {
            log.error("❌ 服务权限验证异常: serviceId={}", serviceId, e);
            return false;
        }
    }

    /**
     * 检查路径是否匹配
     * 支持通配符匹配
     */
    private boolean isPathMatched(String requestPath, String allowedPath) {
        // 精确匹配
        if (requestPath.equals(allowedPath)) {
            return true;
        }

        // 通配符匹配
        if (allowedPath.endsWith("/**")) {
            String prefix = allowedPath.substring(0, allowedPath.length() - 3);
            return requestPath.startsWith(prefix);
        }

        if (allowedPath.endsWith("/*")) {
            String prefix = allowedPath.substring(0, allowedPath.length() - 2);
            return requestPath.startsWith(prefix) && 
                   requestPath.substring(prefix.length()).indexOf('/') == -1;
        }

        // Ant风格路径匹配 (简化版)
        if (allowedPath.contains("*")) {
            return matchAntPath(requestPath, allowedPath);
        }

        return false;
    }

    /**
     * 简化的Ant风格路径匹配
     */
    private boolean matchAntPath(String path, String pattern) {
        // 简单实现，实际可以使用Spring的AntPathMatcher
        String regex = pattern
            .replace("**", "DOUBLE_WILDCARD")
            .replace("*", "[^/]*")
            .replace("DOUBLE_WILDCARD", ".*");
        
        return path.matches(regex);
    }

    /**
     * 获取服务授权统计信息
     */
    public String getAuthStats() {
        RemoteServiceSecurityProperties.ServiceAuth config = properties.getServiceAuth();
        return String.format(
            "ServiceAuth[enabled=%s, allowedServices=%d, requireSameToken=%s, servicePermissions=%s]",
            config.isEnabled(),
            config.getAllowedServices().size(),
            config.isRequireSameToken(),
            config.isEnableServicePermissions()
        );
    }

    /**
     * 获取允许的服务列表
     */
    public List<String> getAllowedServices() {
        return properties.getServiceAuth().getAllowedServices();
    }

    /**
     * 检查特定服务是否被允许
     */
    public boolean isServiceAllowedPublic(String serviceId) {
        return isServiceAllowed(serviceId);
    }

    /**
     * 获取服务的权限配置
     */
    public List<String> getServicePermissions(String serviceId) {
        Map<String, List<String>> servicePermissions = properties.getServiceAuth().getServicePermissions();
        return servicePermissions != null ? servicePermissions.get(serviceId) : null;
    }

    /**
     * 验证服务和路径的权限
     */
    public boolean validateServicePathPermission(String serviceId, String path) {
        if (!isServiceAllowed(serviceId)) {
            return false;
        }

        if (!properties.getServiceAuth().isEnableServicePermissions()) {
            return true;
        }

        List<String> allowedPaths = getServicePermissions(serviceId);
        if (allowedPaths == null || allowedPaths.isEmpty()) {
            return true;
        }

        return allowedPaths.stream().anyMatch(allowedPath -> isPathMatched(path, allowedPath));
    }
}
