package com.oto.common.http.ssl;

import com.oto.common.http.annotation.RemoteServicePort;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 选择性 SSL 拦截器
 * 确保远程服务接口只能通过 HTTPS 访问，主业务接口只能通过 HTTP 访问
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@Slf4j
@Component
public class SelectiveSSLInterceptor implements HandlerInterceptor {

    private final Environment environment;
    private final Set<Class<?>> remoteServiceControllers = ConcurrentHashMap.newKeySet();
    private final int mainPort;
    private final int remotePort;

    public SelectiveSSLInterceptor(ApplicationContext applicationContext, Environment environment) {
        this.environment = environment;
        this.mainPort = environment.getProperty("server.port", Integer.class, 8080);
        this.remotePort = calculateRemotePort();
        
        // 扫描所有带有 @RemoteServicePort 注解的 Controller
        scanRemoteServiceControllers(applicationContext);
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 只处理 Controller 方法
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Class<?> controllerClass = handlerMethod.getBeanType();
        
        // 获取当前请求的端口和协议
        int currentPort = request.getServerPort();
        String scheme = request.getScheme();
        boolean isHttps = "https".equalsIgnoreCase(scheme);
        boolean isRemoteServiceController = isRemoteServiceController(controllerClass);
        
        log.debug("🔍 请求检查: {} {}:{}{} → Controller: {}, 是否远程服务: {}", 
                 scheme.toUpperCase(), request.getServerName(), currentPort, request.getRequestURI(),
                 controllerClass.getSimpleName(), isRemoteServiceController);

        // 规则1: 远程服务接口必须使用 HTTPS
        if (isRemoteServiceController && !isHttps) {
            log.warn("❌ 远程服务接口必须使用 HTTPS 访问: {} → 应该使用 https://localhost:{}{}", 
                    request.getRequestURL(), remotePort, request.getRequestURI());
            
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write(String.format(
                "{\"code\":403,\"msg\":\"远程服务接口必须使用 HTTPS 访问\",\"data\":\"https://localhost:%d%s\"}", 
                remotePort, request.getRequestURI()));
            return false;
        }

        // 规则2: 远程服务接口不能通过主端口访问
        if (isRemoteServiceController && currentPort == mainPort) {
            log.warn("❌ 远程服务接口不能通过主端口访问: {}:{} → 应该使用端口 {}", 
                    currentPort, request.getRequestURI(), remotePort);
            
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write(String.format(
                "{\"code\":404,\"msg\":\"接口不存在\",\"data\":\"请使用正确的端口访问\"}")); 
            return false;
        }

        // 规则3: 主业务接口不能通过远程端口访问
        if (!isRemoteServiceController && currentPort == remotePort) {
            log.warn("❌ 主业务接口不能通过远程端口访问: {}:{} → 应该使用端口 {}", 
                    currentPort, request.getRequestURI(), mainPort);
            
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write(String.format(
                "{\"code\":404,\"msg\":\"接口不存在\",\"data\":\"请使用正确的端口访问\"}")); 
            return false;
        }

        // 规则4: 记录正确的访问
        if (isRemoteServiceController && isHttps && currentPort == remotePort) {
            log.debug("✅ 远程服务接口正确访问: HTTPS:{} → {}", currentPort, controllerClass.getSimpleName());
        } else if (!isRemoteServiceController && !isHttps && currentPort == mainPort) {
            log.debug("✅ 主业务接口正确访问: HTTP:{} → {}", currentPort, controllerClass.getSimpleName());
        }

        return true;
    }

    /**
     * 扫描远程服务 Controller
     */
    private void scanRemoteServiceControllers(ApplicationContext applicationContext) {
        String[] beanNames = applicationContext.getBeanNamesForAnnotation(RemoteServicePort.class);
        
        for (String beanName : beanNames) {
            Object bean = applicationContext.getBean(beanName);
            Class<?> beanClass = bean.getClass();
            
            // 处理代理类
            if (beanClass.getName().contains("$$")) {
                beanClass = beanClass.getSuperclass();
            }
            
            remoteServiceControllers.add(beanClass);
            
            RemoteServicePort annotation = beanClass.getAnnotation(RemoteServicePort.class);
            int annotationPort = annotation != null ? annotation.value() : -1;
            int actualPort = (annotationPort == -1) ? remotePort : annotationPort;
            
            log.info("🔍 发现远程服务 Controller: {} → 端口 {}", beanClass.getSimpleName(), actualPort);
        }
        
        log.info("✅ 扫描完成，发现 {} 个远程服务 Controller", remoteServiceControllers.size());
    }

    /**
     * 检查是否是远程服务 Controller
     */
    private boolean isRemoteServiceController(Class<?> controllerClass) {
        return remoteServiceControllers.contains(controllerClass);
    }

    /**
     * 计算远程端口
     */
    private int calculateRemotePort() {
        // 优先使用配置的端口
        Integer configuredPort = environment.getProperty("oto.common.http.ssl.selective.remote-port", Integer.class);
        if (configuredPort != null && configuredPort > 0) {
            return configuredPort;
        }
        
        // 使用默认偏移量
        return mainPort + 10;
    }

    /**
     * 获取端口信息
     */
    public PortInfo getPortInfo() {
        return new PortInfo(mainPort, remotePort, remoteServiceControllers.size());
    }

    /**
     * 端口信息
     */
    public static class PortInfo {
        private final int mainPort;
        private final int remotePort;
        private final int remoteControllerCount;

        public PortInfo(int mainPort, int remotePort, int remoteControllerCount) {
            this.mainPort = mainPort;
            this.remotePort = remotePort;
            this.remoteControllerCount = remoteControllerCount;
        }

        public int getMainPort() { return mainPort; }
        public int getRemotePort() { return remotePort; }
        public int getRemoteControllerCount() { return remoteControllerCount; }

        @Override
        public String toString() {
            return String.format("PortInfo{mainPort=%d, remotePort=%d, remoteControllers=%d}", 
                               mainPort, remotePort, remoteControllerCount);
        }
    }
}
