package com.oto.common.http.ssl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 选择性 SSL Web 配置
 * 注册拦截器，实现端口和协议的访问控制
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
@ConditionalOnProperty(name = "oto.common.http.ssl.selective-mode", havingValue = "true", matchIfMissing = false)
public class SelectiveSSLWebConfiguration implements WebMvcConfigurer {

    private final SelectiveSSLInterceptor selectiveSSLInterceptor;

    /**
     * 注册选择性 SSL 拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        log.info("🔒 注册选择性 SSL 拦截器");
        
        registry.addInterceptor(selectiveSSLInterceptor)
                .addPathPatterns("/**")
                .order(Integer.MIN_VALUE + 100); // 高优先级，但在端口转发拦截器之后
        
        log.info("✅ 选择性 SSL 拦截器注册完成");
    }

    /**
     * 应用启动完成后显示端口信息
     */
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        displayPortConfiguration();
    }

    /**
     * 显示端口配置信息
     */
    private void displayPortConfiguration() {
        SelectiveSSLInterceptor.PortInfo portInfo = selectiveSSLInterceptor.getPortInfo();
        
        log.info("");
        log.info("🚀 ========== 选择性 SSL 配置完成 ==========");
        log.info("📡 主业务端口 (HTTP):  http://localhost:{}", portInfo.getMainPort());
        log.info("🔒 远程服务端口 (HTTPS): https://localhost:{}", portInfo.getRemotePort());
        log.info("📊 远程服务 Controller 数量: {}", portInfo.getRemoteControllerCount());
        log.info("");
        log.info("📋 访问规则:");
        log.info("  ✅ 主业务接口 → HTTP 端口 {} (如: /api/user, /admin, /actuator)", portInfo.getMainPort());
        log.info("  ✅ 远程服务接口 → HTTPS 端口 {} (如: @RemoteServicePort Controller)", portInfo.getRemotePort());
        log.info("  ❌ 远程服务接口 → HTTP 端口 {} (禁止访问)", portInfo.getMainPort());
        log.info("  ❌ 主业务接口 → HTTPS 端口 {} (禁止访问)", portInfo.getRemotePort());
        log.info("");
        log.info("🔧 FeignClient 配置示例:");
        log.info("  @FeignClient(url = \"https://localhost:{}\", path = \"/remote-api\")", portInfo.getRemotePort());
        log.info("================================================");
        log.info("");
    }
}
