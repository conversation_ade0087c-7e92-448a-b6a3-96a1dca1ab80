package com.oto.common.http.security;

import cn.dev33.satoken.same.SaSameUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * 远程服务Feign拦截器
 * 自动为OpenFeign调用添加安全认证信息
 * 包括签名、时间戳、随机数、服务ID等
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Slf4j
@RequiredArgsConstructor
public class RemoteServiceFeignInterceptor implements RequestInterceptor {

    private final RemoteServiceSecurityProperties securityProperties;
    private final SecureRandom secureRandom = new SecureRandom();

    @Override
    public void apply(RequestTemplate template) {
        // 只对远程服务调用添加安全认证
        if (!shouldApplySecurity(template)) {
            return;
        }

        try {
            // 1. 添加服务ID
            String serviceId = getServiceId();
            template.header("X-Service-Id", serviceId);

            // 2. 添加时间戳
            String timestamp = String.valueOf(System.currentTimeMillis());
            template.header("X-Timestamp", timestamp);

            // 3. 添加随机数
            String nonce = generateNonce();
            template.header("X-Nonce", nonce);

            // 4. 添加Same-Token
            if (securityProperties.getServiceAuth().isRequireSameToken()) {
                String sameToken = SaSameUtil.getToken();
                template.header(SaSameUtil.SAME_TOKEN, sameToken);
            }

            // 5. 计算并添加签名
            if (securityProperties.getSignature().isEnabled()) {
                String signature = calculateSignature(template, timestamp, nonce, serviceId);
                template.header("X-Signature", signature);

                // 详细调试日志
                log.info("🔍 Feign客户端签名调试信息:");
                log.info("  服务ID: {}", serviceId);
                log.info("  时间戳: {}", timestamp);
                log.info("  随机数: {}", nonce);
                log.info("  HTTP方法: {}", template.method());
                log.info("  请求路径: {}", template.path());
                log.info("  查询参数: {}", buildQueryString(template));
                log.info("  请求体哈希: {}", getRequestBodyHash(template));
                log.info("  构建的签名字符串: [{}]", buildSignString(template, timestamp, nonce, serviceId).replace("\n", "\\n"));
                log.info("  计算的签名: {}", signature);
                log.info("  密钥: {}", securityProperties.getSignature().getSecretKey());
                log.info("  算法: {}", securityProperties.getSignature().getAlgorithm());
            }

            log.debug("✅ Feign请求安全认证信息已添加: serviceId={}, target={}",
                     serviceId, template.feignTarget().name());

        } catch (Exception e) {
            log.error("❌ Feign请求安全认证信息添加失败", e);
            throw new RuntimeException("Failed to add security headers to Feign request", e);
        }
    }

    /**
     * 判断是否需要应用安全认证
     */
    private boolean shouldApplySecurity(RequestTemplate template) {
        // 检查是否启用了安全认证
        if (!securityProperties.isEnabled()) {
            return false;
        }

        // 检查目标服务是否需要安全认证
        // 可以根据服务名称或URL判断是否需要安全认证
        // 这里简单地对所有远程服务调用都应用安全认证
        return true;
    }

    /**
     * 获取当前服务ID
     */
    private String getServiceId() {
        // 可以从配置文件或环境变量中获取
        // 这里简单返回固定值，实际应该从配置中获取
        return "oto-admin"; // 可以改为从配置中读取
    }

    /**
     * 生成随机数
     */
    private String generateNonce() {
        byte[] nonceBytes = new byte[16];
        secureRandom.nextBytes(nonceBytes);
        return Base64.getEncoder().encodeToString(nonceBytes);
    }

    /**
     * 计算请求签名
     */
    private String calculateSignature(RequestTemplate template, String timestamp, String nonce, String serviceId) 
            throws Exception {
        
        // 构建签名字符串
        String signString = buildSignString(template, timestamp, nonce, serviceId);
        
        // 计算HMAC签名
        String algorithm = securityProperties.getSignature().getAlgorithm();
        String secretKey = securityProperties.getSignature().getSecretKey();
        
        Mac mac = Mac.getInstance(algorithm);
        SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), algorithm);
        mac.init(secretKeySpec);
        
        byte[] signature = mac.doFinal(signString.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(signature);
    }

    /**
     * 构建签名字符串
     * 与服务端 SignatureValidator.buildSignString() 保持完全一致
     */
    private String buildSignString(RequestTemplate template, String timestamp, String nonce, String serviceId) {
        StringBuilder sb = new StringBuilder();

        // HTTP方法
        sb.append(template.method()).append("\n");

        // 请求URI - 直接使用路径，与服务端 request.getRequestURI() 一致
        String requestURI = getRequestURI(template);
        sb.append(requestURI).append("\n");

        // 查询参数
        sb.append(buildQueryString(template)).append("\n");

        // 请求体哈希
        sb.append(getRequestBodyHash(template)).append("\n");

        // 时间戳
        sb.append(timestamp).append("\n");

        // 随机数
        sb.append(nonce).append("\n");

        // 服务ID
        sb.append(serviceId);

        return sb.toString();
    }

    /**
     * 获取请求URI，与服务端 request.getRequestURI() 保持一致
     */
    private String getRequestURI(RequestTemplate template) {
        try {
            // 构建完整的请求URI，包括FeignClient的path前缀
            String targetUrl = template.feignTarget().url();
            String templatePath = template.path();

            // 构建完整URL
            String fullUrl = targetUrl + templatePath;

            // 解析URL，提取路径部分（这就是服务端接收到的requestURI）
            java.net.URL url = new java.net.URL(fullUrl);
            String requestURI = url.getPath();

            if (requestURI == null || requestURI.isEmpty()) {
                return "/";
            }

            // 移除末尾的斜杠（除非是根路径），与服务端保持一致
            if (requestURI.length() > 1 && requestURI.endsWith("/")) {
                requestURI = requestURI.substring(0, requestURI.length() - 1);
            }

            return requestURI;

        } catch (Exception e) {
            // 降级处理：如果解析失败，使用原始路径
            log.warn("构建请求URI失败，使用原始路径: {}", template.path(), e);
            String path = template.path();
            return (path == null || path.isEmpty()) ? "/" : path;
        }
    }

    /**
     * 构建查询字符串
     */
    private String buildQueryString(RequestTemplate template) {
        if (template.queries() == null || template.queries().isEmpty()) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        template.queries().forEach((key, values) -> {
            if (values != null) {
                for (String value : values) {
                    if (sb.length() > 0) {
                        sb.append("&");
                    }
                    sb.append(key).append("=").append(value != null ? value : "");
                }
            }
        });

        return sb.toString();
    }

    /**
     * 获取请求体哈希
     */
    private String getRequestBodyHash(RequestTemplate template) {
        try {
            byte[] body = template.body();
            if (body == null || body.length == 0) {
                return "";
            }
            
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(body);
            return Base64.getEncoder().encodeToString(hash);
            
        } catch (Exception e) {
            log.warn("计算请求体哈希失败", e);
            return "";
        }
    }

    /**
     * 获取拦截器统计信息
     */
    public String getInterceptorStats() {
        return String.format(
            "RemoteServiceFeignInterceptor[securityEnabled=%s, signatureEnabled=%s, sameTokenEnabled=%s]",
            securityProperties.isEnabled(),
            securityProperties.getSignature().isEnabled(),
            securityProperties.getServiceAuth().isRequireSameToken()
        );
    }
}
