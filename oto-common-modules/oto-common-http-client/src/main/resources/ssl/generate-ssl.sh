#!/bin/bash

# 自动 SSL 证书生成脚本
# 用于同机器部署时自动生成自签名证书
# 
# 使用方法：
# ./generate-ssl.sh [keystore-path] [password]
#
# <AUTHOR>
# @date 2025-08-31

set -e

# 默认配置
DEFAULT_KEYSTORE_PATH="$HOME/.oto/ssl/auto-generated.p12"
DEFAULT_PASSWORD="oto-ssl-2025"
DEFAULT_ALIAS="oto-service"
DEFAULT_VALIDITY="365"

# 参数解析
KEYSTORE_PATH=${1:-$DEFAULT_KEYSTORE_PATH}
PASSWORD=${2:-$DEFAULT_PASSWORD}
ALIAS=${3:-$DEFAULT_ALIAS}
VALIDITY=${4:-$DEFAULT_VALIDITY}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 keytool 是否可用
check_keytool() {
    if ! command -v keytool &> /dev/null; then
        log_error "keytool 命令未找到，请确保已安装 Java JDK"
        exit 1
    fi
    
    log_info "检测到 keytool: $(which keytool)"
}

# 创建目录
create_directories() {
    local dir=$(dirname "$KEYSTORE_PATH")
    if [ ! -d "$dir" ]; then
        log_info "创建目录: $dir"
        mkdir -p "$dir"
    fi
}

# 检查现有证书
check_existing_certificate() {
    if [ -f "$KEYSTORE_PATH" ]; then
        log_warning "证书文件已存在: $KEYSTORE_PATH"
        
        # 检查证书有效期
        local expiry_date=$(keytool -list -v -keystore "$KEYSTORE_PATH" -storepass "$PASSWORD" -alias "$ALIAS" 2>/dev/null | grep "Valid until" | cut -d: -f2- | xargs)
        
        if [ -n "$expiry_date" ]; then
            log_info "现有证书有效期至: $expiry_date"
            
            # 询问是否覆盖
            read -p "是否覆盖现有证书? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                log_info "保留现有证书，退出"
                exit 0
            fi
            
            # 备份现有证书
            local backup_path="${KEYSTORE_PATH}.backup.$(date +%Y%m%d_%H%M%S)"
            log_info "备份现有证书到: $backup_path"
            cp "$KEYSTORE_PATH" "$backup_path"
        fi
    fi
}

# 生成证书
generate_certificate() {
    log_info "开始生成 SSL 证书..."
    
    # 构建 keytool 命令
    local dname="CN=localhost, OU=OTO-System, O=OTO, L=Shanghai, ST=Shanghai, C=CN"
    local san="SAN=DNS:localhost,IP:127.0.0.1,IP:0.0.0.0"
    
    # 删除现有证书（如果存在）
    if [ -f "$KEYSTORE_PATH" ]; then
        rm -f "$KEYSTORE_PATH"
    fi
    
    # 生成证书
    keytool -genkeypair \
        -alias "$ALIAS" \
        -keyalg RSA \
        -keysize 2048 \
        -storetype PKCS12 \
        -keystore "$KEYSTORE_PATH" \
        -storepass "$PASSWORD" \
        -keypass "$PASSWORD" \
        -validity "$VALIDITY" \
        -dname "$dname" \
        -ext "$san" \
        -v
    
    if [ $? -eq 0 ]; then
        log_success "SSL 证书生成成功!"
    else
        log_error "SSL 证书生成失败"
        exit 1
    fi
}

# 验证证书
verify_certificate() {
    log_info "验证生成的证书..."
    
    # 列出证书信息
    keytool -list -v -keystore "$KEYSTORE_PATH" -storepass "$PASSWORD" -alias "$ALIAS"
    
    if [ $? -eq 0 ]; then
        log_success "证书验证通过"
    else
        log_error "证书验证失败"
        exit 1
    fi
}

# 生成配置文件
generate_config() {
    local config_file="$(dirname "$KEYSTORE_PATH")/ssl-config.yml"
    
    log_info "生成配置文件: $config_file"
    
    cat > "$config_file" << EOF
# 自动生成的 SSL 配置
# 生成时间: $(date)
# 证书路径: $KEYSTORE_PATH

server:
  port: 8443
  ssl:
    enabled: true
    key-store: $KEYSTORE_PATH
    key-store-password: $PASSWORD
    key-store-type: PKCS12
    key-alias: $ALIAS

# FeignClient SSL 配置
feign:
  httpclient:
    ssl:
      trust-store: $KEYSTORE_PATH
      trust-store-password: $PASSWORD
      trust-store-type: PKCS12

# OTO 服务配置
oto:
  services:
    admin:
      base-url: https://localhost:8443
    front:
      base-url: https://localhost:8444
  
  common:
    http:
      ssl:
        auto-generate: true
        keystore-path: $KEYSTORE_PATH
        keystore-password: $PASSWORD
        certificate-alias: $ALIAS
EOF

    log_success "配置文件生成完成: $config_file"
}

# 显示使用说明
show_usage() {
    log_info "证书生成完成，使用说明："
    echo
    echo "1. 在 application.yml 中添加以下配置："
    echo "   server:"
    echo "     port: 8443"
    echo "     ssl:"
    echo "       enabled: true"
    echo "       key-store: $KEYSTORE_PATH"
    echo "       key-store-password: $PASSWORD"
    echo "       key-store-type: PKCS12"
    echo
    echo "2. 更新服务地址配置："
    echo "   oto:"
    echo "     services:"
    echo "       admin:"
    echo "         base-url: https://localhost:8443"
    echo
    echo "3. 重启服务以应用 SSL 配置"
    echo
    echo "4. 访问服务："
    echo "   - https://localhost:8443 (忽略浏览器安全警告)"
    echo
    log_warning "注意：这是自签名证书，浏览器会显示安全警告，这是正常的"
}

# 主函数
main() {
    log_info "🔒 OTO 自动 SSL 证书生成器"
    log_info "证书路径: $KEYSTORE_PATH"
    log_info "证书密码: $PASSWORD"
    log_info "证书别名: $ALIAS"
    log_info "有效期: $VALIDITY 天"
    echo
    
    check_keytool
    create_directories
    check_existing_certificate
    generate_certificate
    verify_certificate
    generate_config
    show_usage
    
    log_success "🎉 SSL 证书生成完成!"
}

# 执行主函数
main "$@"
