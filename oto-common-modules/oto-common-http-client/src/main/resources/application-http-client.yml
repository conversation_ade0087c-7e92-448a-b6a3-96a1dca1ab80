# HTTP客户端配置模板（已优化，移除重复默认值）
# 在使用此模块的应用中，需要在 application.yml 中添加以下配置

oto:
  # 统一端口配置（使用新的配置前缀）
  common:
    http:
      # SSL配置
      ssl:
        auto-generate: true
        selective-mode: true
        enabled: true
        keystore-path: ${user.home}/.oto/ssl/${spring.application.name}.p12
        keystore-password: oto-ssl-2025
        certificate-alias: ${spring.application.name}-service
        validity-days: 365
        https-port: ${server.port:8080}  # 根据实际应用设置，主端口+10
        subject: "CN=localhost, OU=OTO-${spring.application.name}, O=OTO, L=Shanghai, ST=Shanghai, C=CN"
        subject-alternative-names:
          - "DNS:localhost"
          - "IP:127.0.0.1"
          - "IP:0.0.0.0"

      port:
        main-port: ${server.port:8080}  # 根据实际应用设置
        # 其他配置使用 HttpPortConfiguration 中的默认值

  # 服务地址配置
  services:
    admin:
      base-url: https://localhost:8090  # oto-admin HTTPS远程服务端口
      # timeout, retry-count, enabled 使用默认值
    front:
      base-url: https://localhost:8091  # oto-front HTTPS远程服务端口
      # timeout, retry-count, enabled 使用默认值
    member:
      base-url: http://localhost:8081   # oto-member HTTP端口
      # timeout, retry-count, enabled 使用默认值

  # 服务安全配置（移除重复默认值）
  security:
    service:
      api-key: "oto-service-secret-key-2025"
      service-name: "oto-unknown"  # 需要根据实际应用设置
      allowed-callers:
        - "oto-admin"
        - "oto-front"
      # enabled=true 使用默认值

# 日志配置
logging:
  level:
    com.oto.common.http: DEBUG
    org.springframework.web.reactive.function.client: DEBUG
