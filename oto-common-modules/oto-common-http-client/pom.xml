<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.oto</groupId>
        <artifactId>oto-common-modules</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>oto-common-http-client</artifactId>

    <name>oto-common-http-client</name>
    <description>OTO应用程序间HTTP通信客户端</description>

    <dependencies>

        <!-- 通用核心模块 -->
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-core</artifactId>
        </dependency>

        <!-- MyBatis模块 -->
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-mybatis</artifactId>
        </dependency>

        <!-- JSON模块 -->
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-json</artifactId>
        </dependency>

        <!-- Spring Boot Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- WebClient (推荐) -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>

        <!-- Apache HttpClient (备选) -->
        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
        </dependency>

        <!-- SpringBoot Configuration Processor -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Validation -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- Spring Cloud OpenFeign -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <!-- Sa-Token 核心依赖模块 -->
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-satoken-core</artifactId>
        </dependency>

    </dependencies>

</project>
