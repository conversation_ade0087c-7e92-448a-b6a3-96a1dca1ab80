# OTO HTTP Client 公共模块使用指南

## 📋 概述

OTO HTTP Client 公共模块提供了全面的安全保障机制和动态端口配置，包括：
- **FeignClient 安全检查** - 防止 Controller 直接注入 FeignClient
- **动态端口配置** - 根据主端口自动计算其他服务端口
- **多端口权限验证** - Sa-Token 集成的权限验证机制

## 🚀 快速开始

### 1. 引入依赖

在项目的 `pom.xml` 中引入依赖：

```xml
<dependency>
    <groupId>com.oto</groupId>
    <artifactId>oto-common-http-client</artifactId>
</dependency>
```

### 2. 配置选项

在 `application.yml` 中添加配置：

```yaml
oto:
  common:
    http:
      # 动态端口配置
      dynamic-port:
        enabled: true
        main-port: ${server.port:8080}  # 主端口
        port-offset: 10                 # 端口偏移量
        address: 127.0.0.1              # 绑定地址
        internal-context-path: /internal
        management-context-path: /actuator

      # 安全配置
      security:
        enabled: true
        feign-client:
          enabled: true
          direct-access: false  # 禁止直接访问 FeignClient
          only-service-layer: true  # 只允许 Service 层访问
```

### 3. 自动生效

安全检查器会在应用启动时自动生效，无需额外配置。

## 🔒 安全功能

### 1. 编译时检查

- **FeignClientSecurityChecker**: 在 Bean 创建前检查架构合规性
- 防止 Controller 直接注入 FeignClient
- 检测可疑的类名和路径模式

### 2. 运行时监控

- **HttpSecurityMonitor**: 持续监控安全状态
- 提供健康检查端点
- 收集安全指标

### 3. 安全审计

- **HttpSecurityAuditor**: 记录安全事件
- 审计 FeignClient 使用情况
- 审计 Controller 暴露情况

## ⚙️ 配置选项

### 完整配置示例

```yaml
oto:
  common:
    http:
      security:
        enabled: true
        feign-client:
          enabled: true
          direct-access: false
          only-service-layer: true
          allowed-callers:
            - "Service"
            - "InternalService"
            - "BusinessService"
          forbidden-callers:
            - "Controller"
            - "RestController"
            - "FeignController"
          suspicious-patterns:
            - "feign"
            - "FeignController"
            - "RemoteController"
          suspicious-paths:
            - "feign"
            - "remote"
            - "/internal"
        monitoring:
          enabled: true
          interval: 300000  # 5分钟
          health-check: true
          metrics: true
          alert-threshold:
            security-violation-count: 5
            memory-usage-threshold: 0.9
            cpu-usage-threshold: 0.8
        audit:
          enabled: true
          log-level: "INFO"
          detailed: false
          event-types:
            - "FEIGN_CLIENT_ACCESS"
            - "CONTROLLER_EXPOSURE"
            - "SECURITY_VIOLATION"
```

## 🎯 最佳实践

### 1. 正确的架构模式

✅ **推荐**：
```java
@RestController
public class BusinessController {
    
    @Autowired
    private BusinessService businessService;  // 注入 Service
    
    @GetMapping("/api/data")
    public R<Data> getData() {
        return businessService.getData();  // 通过 Service 调用
    }
}

@Service
public class BusinessService {
    
    @Autowired
    private DataFeignClient dataFeignClient;  // Service 层注入 FeignClient
    
    public R<Data> getData() {
        return dataFeignClient.getData();  // Service 层调用 FeignClient
    }
}
```

❌ **错误**：
```java
@RestController
public class BadController {
    
    @Autowired
    private DataFeignClient dataFeignClient;  // ❌ Controller 直接注入 FeignClient
    
    @GetMapping("/api/data")
    public R<Data> getData() {
        return dataFeignClient.getData();  // ❌ Controller 直接调用 FeignClient
    }
}
```

### 2. 内部服务模式

对于需要内部调用的场景，使用内部服务：

```java
@Service
public class InternalService {
    
    @Autowired
    private DataFeignClient dataFeignClient;
    
    @PreAuthorize("hasRole('INTERNAL')")
    public R<Data> getInternalData() {
        return dataFeignClient.getData();
    }
}
```

## 🔍 监控和诊断

### 1. 健康检查

访问健康检查端点：
```
GET /actuator/health/httpSecurity
```

### 2. 安全日志

查看安全相关日志：
```
[INFO] 🔍 [公共模块] 开始 FeignClient 安全检查...
[INFO] ✅ [公共模块] FeignClient 安全检查通过
```

### 3. 安全违规处理

如果检测到安全违规，应用会：
1. 记录详细的错误日志
2. 抛出 SecurityException
3. 阻止应用启动

## 🚨 故障排除

### 1. 常见错误

**错误**: `Controller 'XXX' 直接注入了 FeignClient 'YYY'`

**解决方案**: 
1. 移除 Controller 中的 FeignClient 注入
2. 创建 Service 层来封装 FeignClient 调用
3. Controller 注入 Service 而不是 FeignClient

### 2. 禁用安全检查

如果需要临时禁用安全检查：

```yaml
oto:
  common:
    http:
      security:
        enabled: false
```

或者只禁用 FeignClient 检查：

```yaml
oto:
  common:
    http:
      security:
        feign-client:
          enabled: false
```

## 📚 扩展开发

### 1. 自定义安全检查

可以扩展 `FeignClientSecurityChecker` 来添加自定义检查逻辑。

### 2. 自定义监控

可以实现自己的监控组件来收集特定的安全指标。

## 🤝 贡献

如果发现安全漏洞或有改进建议，请联系开发团队。

## 📄 许可证

本模块遵循 OTO 项目的许可证协议。
