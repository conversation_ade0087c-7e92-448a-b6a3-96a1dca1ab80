# Controller 级别端口隔离使用指南

## 📋 概述

本功能实现了 Controller 级别的端口隔离，允许将特定的 Controller 绑定到指定端口，实现真正的端口分离。

## 🎯 使用场景

### 典型需求
```
主端口 8080：
├── 普通业务接口 ✅ 可访问
├── 前端页面接口 ✅ 可访问  
└── TestController ❌ 不可访问

远程服务端口 8090：
├── TestController ✅ 可访问（供 OpenFeign 调用）
├── TestService ✅ 可访问（供 OpenFeign 调用）
└── 其他业务接口 ❌ 不可访问
```

### 应用场景
- **微服务间通信** - 某些接口只供内部服务调用
- **OpenFeign 专用接口** - 避免在主端口暴露内部接口
- **安全隔离** - 敏感接口与普通接口分离
- **负载均衡** - 不同类型的请求分流到不同端口

## 🚀 快速开始

### 1. 引入依赖
```xml
<dependency>
    <groupId>com.oto</groupId>
    <artifactId>oto-common-http-client</artifactId>
</dependency>
```

### 2. 配置端口
```yaml
oto:
  common:
    http:
      remote-service-port:
        enabled: true
        main-port: 8080
        remote-service-ports:
          - 8090
          - 8091
        auto-scan: true
        print-port-mapping: true
```

### 3. 使用注解
```java
// 远程服务 Controller（只能在 8090 端口访问）
@RestController
@RemoteServicePort(8090)
public class TestController {
    
    @GetMapping("/test")
    public R<String> test() {
        return R.ok("只能通过 8090 端口访问");
    }
}

// 普通业务 Controller（只能在 8080 主端口访问）
@RestController
public class BusinessController {
    
    @GetMapping("/business")
    public R<String> business() {
        return R.ok("只能通过 8080 端口访问");
    }
}
```

## 🔧 配置详解

### 完整配置示例
```yaml
oto:
  common:
    http:
      remote-service-port:
        enabled: true                    # 是否启用端口隔离
        main-port: 8080                  # 主端口
        remote-service-ports:            # 远程服务端口列表
          - 8090
          - 8091
          - 8092
        auto-scan: true                  # 自动扫描 @RemoteServicePort 注解
        validate-on-startup: true       # 启动时验证配置
        print-port-mapping: true        # 打印端口映射信息
```

### 配置说明
- **enabled**: 是否启用端口隔离功能
- **main-port**: 主业务端口，没有注解的 Controller 只能在此端口访问
- **remote-service-ports**: 远程服务端口列表，可配置多个
- **auto-scan**: 是否自动扫描带有 `@RemoteServicePort` 注解的 Controller
- **validate-on-startup**: 是否在应用启动时验证端口配置
- **print-port-mapping**: 是否在启动时打印端口映射信息

## 📝 注解使用

### @RemoteServicePort 注解
```java
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RemoteServicePort {
    /**
     * 指定该 Controller 只能访问的端口
     */
    int value();
    
    /**
     * 描述信息
     */
    String description() default "远程服务端口";
}
```

### 使用示例
```java
// 示例1：基础使用
@RestController
@RemoteServicePort(8090)
public class UserRemoteController {
    // 该 Controller 只能通过 8090 端口访问
}

// 示例2：带描述
@RestController
@RemoteServicePort(value = 8091, description = "订单服务专用端口")
public class OrderRemoteController {
    // 该 Controller 只能通过 8091 端口访问
}

// 示例3：普通 Controller（无注解）
@RestController
public class WebController {
    // 该 Controller 只能通过主端口 8080 访问
}
```

## 🔒 访问控制规则

### 端口隔离规则
1. **有 @RemoteServicePort 注解的 Controller**
   - 只能通过注解指定的端口访问
   - 主端口无法访问
   - 其他远程服务端口也无法访问

2. **没有注解的 Controller**
   - 只能通过主端口访问
   - 远程服务端口无法访问

### 访问结果
```
请求: GET http://localhost:8080/test
结果: 404 Not Found (TestController 不能在主端口访问)

请求: GET http://localhost:8090/test  
结果: 200 OK (TestController 可以在 8090 端口访问)

请求: GET http://localhost:8080/business
结果: 200 OK (BusinessController 可以在主端口访问)

请求: GET http://localhost:8090/business
结果: 404 Not Found (BusinessController 不能在远程端口访问)
```

## 📊 启动日志示例

### 端口映射信息
```
🔍 开始扫描远程服务端口配置...
扫描到 2 个远程服务端口: [8090, 8091]
✅ 远程服务端口配置扫描完成

📋 端口映射信息:
  主端口: 8080 (普通业务 Controller)
  远程服务端口: 8090 (带 @RemoteServicePort 注解的 Controller)
  远程服务端口: 8091 (带 @RemoteServicePort 注解的 Controller)

📋 Controller 端口映射:
  TestController -> 端口 8090 (远程服务)
  UserRemoteController -> 端口 8091 (远程服务)
  BusinessController -> 端口 8080 (主端口)
  WebController -> 端口 8080 (主端口)

注册端口隔离拦截器
```

### 运行时日志
```
✅ 远程服务访问: Controller 'TestController' 通过端口 8090 访问成功
❌ 端口隔离拦截: Controller 'TestController' 要求端口 8090, 当前端口 8080, 请求路径: /test
✅ 主端口访问: Controller 'BusinessController' 通过主端口 8080 访问成功
❌ 端口隔离拦截: Controller 'BusinessController' 只能通过主端口 8080 访问, 当前端口 8090, 请求路径: /business
```

## 🛠️ 多端口服务器配置

### Spring Boot 多端口配置
```java
@Configuration
public class MultiPortConfiguration {
    
    @Bean
    public ServletWebServerFactory servletContainer() {
        TomcatServletWebServerFactory tomcat = new TomcatServletWebServerFactory();
        
        // 添加额外的连接器
        tomcat.addAdditionalTomcatConnectors(createConnector(8090));
        tomcat.addAdditionalTomcatConnectors(createConnector(8091));
        
        return tomcat;
    }
    
    private Connector createConnector(int port) {
        Connector connector = new Connector(TomcatServletWebServerFactory.DEFAULT_PROTOCOL);
        connector.setPort(port);
        return connector;
    }
}
```

## 🔍 故障排除

### 常见问题

#### 1. Controller 无法访问
```
问题: 访问 Controller 返回 404
原因: 端口不匹配
解决: 检查 @RemoteServicePort 注解和访问端口是否一致
```

#### 2. 端口冲突
```
问题: 应用启动失败，端口被占用
原因: 远程服务端口与其他服务冲突
解决: 修改 remote-service-ports 配置
```

#### 3. 注解未生效
```
问题: @RemoteServicePort 注解不起作用
原因: auto-scan 被禁用或配置错误
解决: 确保 auto-scan: true 且端口在 remote-service-ports 列表中
```

## 🎯 最佳实践

### 1. 端口规划
```yaml
# 推荐的端口规划
main-port: 8080          # 主业务端口
remote-service-ports:
  - 8090                 # 用户服务
  - 8091                 # 订单服务  
  - 8092                 # 支付服务
```

### 2. Controller 命名
```java
// 推荐命名规范
@RemoteServicePort(8090)
public class UserRemoteController {  // 远程服务 Controller
}

public class UserWebController {     // Web 业务 Controller
}
```

### 3. 安全考虑
- 远程服务端口只绑定内网地址
- 使用防火墙限制端口访问
- 定期审查端口映射配置

这种 Controller 级别的端口隔离机制完美解决了您的需求，实现了真正的接口分离！
