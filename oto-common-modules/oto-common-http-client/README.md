# OTO HTTP 客户端模块

## 📋 模块介绍

`oto-common-http-client` 是 OTO 系统中用于应用程序间 HTTP 通信的客户端模块。提供了标准化的服务调用、认证、日志记录和监控功能。

## 🚀 快速开始

### 1. 添加依赖

在需要使用的模块的 `pom.xml` 中添加依赖：

```xml
<dependency>
    <groupId>com.oto</groupId>
    <artifactId>oto-common-http-client</artifactId>
</dependency>
```

### 2. 配置文件

在 `application.yml` 中添加配置：

```yaml
oto:
  services:
    admin:
      base-url: http://localhost:8080
      timeout: 5000
      retry-count: 3
    member:
      base-url: http://localhost:8081
      timeout: 5000
      retry-count: 3
  security:
    service:
      api-key: "your-secret-api-key"
      enabled: true
      service-name: "oto-admin"  # 当前应用名称
```

### 3. 使用声明式远程服务

```java
@Service
public class YourService {

    @Autowired
    private AdminRemoteService adminRemoteService;

    @Autowired
    private MemberRemoteService memberRemoteService;

    public void example() {
        // 调用后管服务 - 声明式接口，类型安全
        Map<String, Object> health = adminRemoteService.healthCheck();

        // 调用会员服务 - 像本地方法调用一样简单
        Map<String, Object> health2 = memberRemoteService.healthCheck();
    }
}
```

## 🔧 功能特性

### 1. 自动认证
- 服务间 API Key 认证
- 用户 Token 自动传递
- 请求头自动添加

### 2. 日志监控
- 自动记录请求响应日志
- 集成通信监控模块
- 性能指标统计

### 3. 错误处理
- 统一异常处理
- 自动重试机制
- 降级处理支持

### 4. 配置灵活
- 支持多环境配置
- 超时时间可配置
- 服务地址动态配置

## 📊 可用的声明式远程服务

### AdminRemoteService (后管远程服务接口)

```java
// 声明式接口定义
@RemoteService(name = "admin-service", path = "/admin/api")
public interface AdminRemoteService {
    @GetApi("/health/check")
    Map<String, Object> healthCheck();

    // 根据实际需求添加其他接口方法
}

// 使用方式 - 像调用本地方法一样
Map<String, Object> health = adminRemoteService.healthCheck();
```

### MemberRemoteService (会员远程服务接口)

```java
// 声明式接口定义
@RemoteService(name = "member-service", path = "/app/api")
public interface MemberRemoteService {
    @GetApi("/health/check")
    Map<String, Object> healthCheck();

    @GetApi("/otoconfig/diamond-position/{id}")
    Map<String, Object> getDiamondPositionById(@PathVariable("id") Long id);

    // 根据实际需求添加其他接口方法
}

// 使用方式 - 类型安全，IDE友好
Map<String, Object> health = memberRemoteService.healthCheck();
Map<String, Object> position = memberRemoteService.getDiamondPositionById(1L);

// 会员注册
Map<String, Object> registerData = Map.of("name", "张三", "phone", "13800138000");
memberServiceClient.registerMember(registerData);

// 获取会员订单
List<Map<String, Object>> orders = memberServiceClient.getMemberOrders(1L);

// 更新会员状态
memberServiceClient.updateMemberStatus(1L, "ACTIVE", "管理员激活");
```

## 🔒 安全机制

### 1. 服务间认证
- 使用 `X-Service-Key` 头进行服务认证
- 使用 `X-Service-Name` 标识调用方
- 支持白名单机制

### 2. 用户认证传递
- 自动传递用户 Token
- 支持多种用户类型
- 保持用户上下文

## 📈 监控和日志

### 1. 自动日志记录
- 请求响应自动记录
- 性能指标统计
- 错误信息详细记录

### 2. 集成监控模块
- 使用 `AppCommunicationUtil` 记录通信日志
- 支持链路追踪
- 提供统计分析

## ⚙️ 高级配置

### 1. 自定义 WebClient

```java
@Configuration
public class CustomHttpConfig {
    
    @Bean
    @Primary
    public WebClient customWebClient() {
        return WebClient.builder()
            .baseUrl("http://custom-service")
            .defaultHeader("Custom-Header", "value")
            .build();
    }
}
```

### 2. 自定义拦截器

```java
@Component
public class CustomInterceptor implements ClientHttpRequestInterceptor {
    
    @Override
    public ClientHttpResponse intercept(
            HttpRequest request, 
            byte[] body, 
            ClientHttpRequestExecution execution) throws IOException {
        // 自定义逻辑
        return execution.execute(request, body);
    }
}
```

## 🐛 故障排查

### 1. 常见问题

**连接超时**
```
检查服务地址配置是否正确
检查网络连通性
调整超时时间配置
```

**认证失败**
```
检查 API Key 配置
检查服务名称配置
确认目标服务的认证机制
```

**序列化错误**
```
检查响应格式是否为标准 R<T> 格式
确认数据类型匹配
检查 JSON 序列化配置
```

### 2. 调试模式

```yaml
logging:
  level:
    com.oto.common.http: DEBUG
    org.springframework.web.reactive.function.client: DEBUG
```

## 📝 更新日志

### v1.0.0 (2025-08-30)
- 初始版本发布
- 支持基础的 HTTP 客户端功能
- 集成认证和日志监控
- 提供后管和会员服务客户端
