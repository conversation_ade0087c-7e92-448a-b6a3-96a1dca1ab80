<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.oto</groupId>
        <artifactId>oto-common-modules</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>remote-front-api</artifactId>
    <description>远程前端API调用模块</description>

    <dependencies>
        <!-- 通用工具 -->
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-core</artifactId>
        </dependency>

        <!-- Web模块 -->
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-web</artifactId>
        </dependency>

        <!-- HTTP客户端模块 -->
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-http-client</artifactId>
        </dependency>

        <!-- 日志记录 -->
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-log</artifactId>
        </dependency>

        <!-- 安全模块 -->
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-security</artifactId>
        </dependency>

        <!-- 数据库 -->
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-mybatis</artifactId>
        </dependency>

        <!-- 通用远程调用领域模型 -->
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-remote-domain</artifactId>
        </dependency>



        <!-- 参数校验 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- Spring Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-remote-domain</artifactId>
        </dependency>
    </dependencies>

</project>
