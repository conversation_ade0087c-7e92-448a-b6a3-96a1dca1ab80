package com.oto.admin.remote.feign;


import com.oto.common.core.domain.R;
import com.oto.common.core.validate.AddGroup;
import com.oto.common.core.validate.EditGroup;
import com.oto.common.domain.bo.OtoDiamondPreviewVersionBo;
import com.oto.common.domain.request.CreateVersionRequest;
import com.oto.common.domain.vo.OtoDiamondPreviewVersionVo;
import com.oto.common.http.config.FeignConfig;
import com.oto.common.log.annotation.Log;
import com.oto.common.log.enums.BusinessType;
import com.oto.common.mybatis.core.page.PageQuery;
import com.oto.common.mybatis.core.page.TableDataInfo;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import jakarta.servlet.http.HttpServletResponse;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 预览版本管理 OpenFeign 客户端
 * 对应控制器: OtoDiamondPreviewVersionController
 * 基础路径: /app/api/otoconfig/diamond-preview
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@FeignClient(
    name = "diamond-preview-version-service",
    url = "${oto.services.front.base-url:https://localhost:8091}",
    path = "/app/api/otoconfig/diamond-preview",
    configuration = FeignConfig.class
)
public interface DiamondPreviewVersionFeignClient {

    /**
     * 分页查询预览版本列表
     */
    @GetMapping("/versions")
    public R<TableDataInfo<OtoDiamondPreviewVersionVo>> page(@SpringQueryMap OtoDiamondPreviewVersionBo bo,@SpringQueryMap PageQuery pageQuery);

    /**
     * 导出预览版本列表
     */
    @Log(title = "金刚位预览版本", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@RequestParam Map<String, Object> params);

    /**
     * 获取当前发布版本
     */
    @GetMapping("/current")
    public R<OtoDiamondPreviewVersionVo> getCurrentVersion();

    /**
     * 根据版本编码获取预览版本详情
     */
    @GetMapping("/{versionCode}")
    public R<OtoDiamondPreviewVersionVo> getByVersionCode(@PathVariable String versionCode);

    /**
     * 根据ID获取预览版本详情
     */
    @GetMapping("/detail/{id}")
    public R<OtoDiamondPreviewVersionVo> getInfo(@NotNull(message = "主键不能为空")
                                                 @PathVariable Long id);

    /**
     * 新增预览版本
     */
    @Log(title = "金刚位预览版本", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OtoDiamondPreviewVersionBo bo);

    /**
     * 创建预览版本（基于当前配置）
     */
    @Log(title = "创建预览版本", businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public R<String> createPreviewVersion(@RequestBody CreateVersionRequest request);

    /**
     * 修改预览版本
     */
    @Log(title = "金刚位预览版本", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OtoDiamondPreviewVersionBo bo);

    /**
     * 更新版本状态
     */
    @Log(title = "更新版本状态", businessType = BusinessType.UPDATE)
    @PutMapping("/{versionCode}/status")
    public R<Void> updateStatus(@PathVariable String versionCode, @RequestBody Map<String, Integer> request);

    /**
     * 发布版本
     */
    @Log(title = "发布预览版本", businessType = BusinessType.UPDATE)
    @PostMapping("/{versionCode}/publish")
    public R<Void> publishVersion(@PathVariable String versionCode);

    /**
     * 回滚版本
     */
    @Log(title = "回滚预览版本", businessType = BusinessType.UPDATE)
    @PostMapping("/{versionCode}/rollback")
    public R<Void> rollbackVersion(@PathVariable String versionCode);

    /**
     * 生成预览URL
     */
    @GetMapping("/{versionCode}/preview-url")
    public R<String> generatePreviewUrl(@PathVariable String versionCode);

    /**
     * 删除预览版本
     */
    @Log(title = "金刚位预览版本", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids);





}
