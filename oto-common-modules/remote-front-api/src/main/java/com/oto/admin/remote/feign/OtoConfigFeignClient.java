package com.oto.admin.remote.feign;

import com.oto.common.core.domain.R;
import com.oto.common.domain.bo.OtoConfigBo;
import com.oto.common.domain.vo.OtoConfigVo;
import com.oto.common.http.config.FeignConfig;
import com.oto.common.mybatis.core.page.PageQuery;
import com.oto.common.mybatis.core.page.TableDataInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * oto参数配置远程调用客户端
 * 对应控制器: RemoteOtoConfigController
 * 基础路径: /app/api/otoconfig/otoconfig
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@FeignClient(
    name = "oto-config-service",
    url = "${oto.services.front.base-url:https://localhost:8091}",
    path = "/app/api/otoconfig/otoconfig",
    configuration = FeignConfig.class
)
public interface OtoConfigFeignClient {

    /**
     * 查询oto参数配置列表
     *
     * @param bo 查询条件
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    @GetMapping("/list")
    TableDataInfo<OtoConfigVo> list(@SpringQueryMap OtoConfigBo bo, @SpringQueryMap PageQuery pageQuery);

    /**
     * 导出oto参数配置列表
     *
     * @param bo 查询条件
     * @return 配置列表
     */
    @PostMapping("/export")
    R<List<OtoConfigVo>> export(@RequestBody OtoConfigBo bo);

    /**
     * 获取oto参数配置详细信息
     *
     * @param configId 主键
     * @return 配置详情
     */
    @GetMapping("/{configId}")
    R<OtoConfigVo> getInfo(@PathVariable("configId") Long configId);

    /**
     * 新增oto参数配置
     *
     * @param bo 配置信息
     * @return 操作结果
     */
    @PostMapping
    R<String> add(@RequestBody OtoConfigBo bo);

    /**
     * 修改oto参数配置
     *
     * @param bo 配置信息
     * @return 操作结果
     */
    @PutMapping
    R<String> edit(@RequestBody OtoConfigBo bo);

    /**
     * 删除oto参数配置
     *
     * @param configIds 主键串
     * @return 操作结果
     */
    @DeleteMapping("/{configIds}")
    R<Void> remove(@PathVariable("configIds") Long[] configIds);

    /**
     * 刷新所有配置缓存
     *
     * @return 操作结果
     */
    @DeleteMapping("/refresh")
    R<Void> refreshCache();

    /**
     * 根据配置键获取配置值
     *
     * @param configKey 配置键
     * @return 配置值
     */
    @GetMapping("/value/{configKey}")
    R<String> getConfigValueByKey(@PathVariable("configKey") String configKey);

    /**
     * 根据配置键获取开关配置值
     *
     * @param configKey 配置键
     * @return 开关值
     */
    @GetMapping("/enable/{configKey}")
    R<Boolean> getEnableByConfigKey(@PathVariable("configKey") String configKey);
}
