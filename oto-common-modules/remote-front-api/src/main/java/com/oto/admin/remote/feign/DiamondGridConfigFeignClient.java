package com.oto.admin.remote.feign;

import com.oto.common.core.domain.R;
import com.oto.common.domain.bo.OtoDiamondGridConfigBo;
import com.oto.common.domain.vo.OtoDiamondGridConfigVo;
import com.oto.common.http.config.FeignConfig;
import com.oto.common.log.annotation.Log;
import com.oto.common.log.enums.BusinessType;
import com.oto.common.mybatis.core.page.PageQuery;
import com.oto.common.mybatis.core.page.TableDataInfo;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 网格配置管理 OpenFeign 客户端
 * 对应控制器: DiamondGridConfigController
 * 基础路径: /app/api/otoconfig/diamond-grid
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@FeignClient(
    name = "diamond-grid-config-service",
    url = "${oto.services.front.base-url:https://localhost:8091}",
    path = "/app/api/otoconfig/diamond-grid",
    configuration = FeignConfig.class
)
public interface DiamondGridConfigFeignClient {

    @GetMapping("/config")
    public R<OtoDiamondGridConfigVo> getActiveConfig();

    /**
     * 分页查询网格配置列表（兼容前端，实际只返回当前配置）
     */
    @GetMapping("/page")
    public R<OtoDiamondGridConfigVo> page();

    /**
     * 查询网格配置详细
     */
    @GetMapping("/{id}")
    public R<OtoDiamondGridConfigVo> getInfo(@PathVariable Long id);

    /**
     * 新增网格配置（实际是更新配置）
     */
    @Log(title = "金刚位网格配置", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Valid @RequestBody OtoDiamondGridConfigVo config);

    /**
     * 修改网格配置
     */
    @Log(title = "金刚位网格配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Valid @RequestBody OtoDiamondGridConfigVo config);

    /**
     * 新增行配置
     */
    @Log(title = "新增网格行配置", businessType = BusinessType.INSERT)
    @PostMapping("/row")
    public R<Void> addRow(@Valid @RequestBody OtoDiamondGridConfigVo.RowConfig rowConfig);

    /**
     * 编辑行配置
     */
    @Log(title = "编辑网格行配置", businessType = BusinessType.UPDATE)
    @PutMapping("/row/{rowId}")
    public R<Void> editRow(@PathVariable Integer rowId, @Valid @RequestBody OtoDiamondGridConfigVo.RowConfig rowConfig);

    /**
     * 删除指定行配置
     */
    @Log(title = "删除网格行配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/row/{rowId}")
    public R<Void> removeRow(@PathVariable Integer rowId);

    /**
     * 删除网格配置（不支持，返回错误）
     */
    @Log(title = "金刚位网格配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@PathVariable Long[] ids);

    /**
     * 激活指定的网格配置（兼容接口，实际无操作）
     */
    @Log(title = "激活网格配置", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/activate")
    public R<Void> activateConfig(@PathVariable Long id);

    /**
     * 获取网格配置（包含行配置）
     */
    @GetMapping("/{id}/with-rows")
    public R<OtoDiamondGridConfigVo> getConfigWithRows(@PathVariable Long id);
}
