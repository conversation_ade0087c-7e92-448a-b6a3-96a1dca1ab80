package com.oto.admin.remote.feign;

import com.oto.common.core.domain.R;
import com.oto.common.domain.model.GridLayoutData;
import com.oto.common.domain.request.AutoAssignRequest;
import com.oto.common.domain.request.PositionUpdateRequest;
import com.oto.common.domain.vo.OtoDiamondPositionVo;
import com.oto.common.http.config.FeignConfig;
import com.oto.common.log.annotation.Log;
import com.oto.common.log.enums.BusinessType;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 布局管理 OpenFeign 客户端
 * 对应控制器: DiamondLayoutController
 * 基础路径: /app/api/otoconfig/diamond-layout
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@FeignClient(
    name = "diamond-layout-service",
    url = "${oto.services.front.base-url:https://localhost:8091}",
    path = "/app/api/otoconfig/diamond-layout",
    configuration = FeignConfig.class
)
public interface DiamondLayoutFeignClient {

    /**
     * 获取完整的网格布局数据
     */
    @GetMapping("/complete")
    public R<GridLayoutData> getCompleteLayout();

    /**
     * 获取未使用的金刚位列表
     */
    @GetMapping("/unused")
    public R<List<OtoDiamondPositionVo>> getUnusedPositions();

    /**
     * 批量更新金刚位位置
     */
    @Log(title = "金刚位位置更新", businessType = BusinessType.UPDATE)
    @PutMapping("/positions")
    public R<Void> updatePositions(@RequestBody PositionUpdateRequest request);



    /**
     * 自动分配位置
     */
    @Log(title = "金刚位自动分配", businessType = BusinessType.UPDATE)
    @PostMapping("/auto-assign")
    public R<Void> autoAssignPositions(@RequestBody AutoAssignRequest request);

}
