package com.oto.admin.remote.feign;


import com.oto.common.core.domain.R;
import com.oto.common.core.validate.AddGroup;
import com.oto.common.core.validate.EditGroup;
import com.oto.common.domain.bo.OtoDiamondPositionBo;
import com.oto.common.domain.vo.OtoDiamondPositionVo;
import com.oto.common.http.config.FeignConfig;
import com.oto.common.log.annotation.Log;
import com.oto.common.log.enums.BusinessType;
import com.oto.common.mybatis.core.page.PageQuery;
import com.oto.common.mybatis.core.page.TableDataInfo;
import jakarta.validation.constraints.NotNull;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 金刚位配置远程调用客户端
 * 对应控制器: OtoDiamondPositionController
 * 基础路径: /app/api/otoconfig/diamond-position
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@FeignClient(
    name = "diamond-position-service",
    url = "${oto.services.front.base-url:https://localhost:8091}",
    path = "/app/api/otoconfig/diamond-position",
    configuration = FeignConfig.class
)
public interface DiamondPositionFeignClient {

    /**
     * 分页查询金刚位列表
     */
    @GetMapping("/page")
    public R<TableDataInfo<OtoDiamondPositionVo>> page(@SpringQueryMap OtoDiamondPositionBo bo,@SpringQueryMap PageQuery pageQuery) ;

    /**
     * 获取所有启用的金刚位（用于前端展示）
     */
    @GetMapping("/list")
    public R<List<OtoDiamondPositionVo>> list();



    /**
     * 根据ID获取金刚位详情
     */
    @GetMapping("/{id}")
    public R<OtoDiamondPositionVo> getInfo(@NotNull(message = "主键不能为空")
                                           @PathVariable Long id);

    /**
     * 新增金刚位
     */
    @Log(title = "金刚位配置", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OtoDiamondPositionBo bo);

    /**
     * 更新金刚位
     */
    @Log(title = "金刚位配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OtoDiamondPositionBo bo);

    /**
     * 删除金刚位
     */
    @Log(title = "金刚位配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<Void> remove(@NotNull(message = "主键不能为空")
                          @PathVariable Long id);



    /**
     * 更新金刚位排序
     */
    @Log(title = "金刚位配置", businessType = BusinessType.UPDATE)
    @PutMapping("/sort")
    public R<Void> updateSort(@RequestBody List<OtoDiamondPositionBo> sortList);

    /**
     * 启用/禁用金刚位
     */
    @Log(title = "金刚位配置", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/status")
    public R<Void> updateStatus(@PathVariable Long id,
                                @RequestParam Integer status);

    /**
     * 批量更新状态
     */
    @Log(title = "金刚位配置", businessType = BusinessType.UPDATE)
    @PatchMapping("/batch/status")
    public R<Void> batchUpdateStatus(@RequestBody Map<String, Object> request);


}
