<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.oto</groupId>
        <artifactId>oto-common-modules</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>oto-common-remote-domain</artifactId>

    <description>
        oto-common-remote-domain 通用远程调用领域模型模块
        包含远程调用相关的 VO、DTO、Request、Response 等数据传输对象
    </description>

    <dependencies>
        <!-- 通用工具 -->
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-core</artifactId>
        </dependency>

        <!-- JSON 处理 -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <!-- 参数校验 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-excel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-tenant</artifactId>
        </dependency>
    </dependencies>

</project>
