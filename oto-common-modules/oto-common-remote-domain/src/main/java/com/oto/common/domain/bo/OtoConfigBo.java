package com.oto.common.domain.bo;

import com.oto.common.core.validate.AddGroup;
import com.oto.common.core.validate.EditGroup;
import com.oto.common.domain.OtoConfig;
import com.oto.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * oto参数配置业务对象 oto_config
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = OtoConfig.class, reverseConvertGenerate = false)
public class OtoConfigBo extends BaseEntity {

    /**
     * 参数主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long configId;

    /**
     * 参数名称
     */
    @NotBlank(message = "参数名称不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 100, message = "参数名称长度不能超过100个字符")
    private String configName;

    /**
     * 参数键名
     */
    @NotBlank(message = "参数键名不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 100, message = "参数键名长度不能超过100个字符")
    private String configKey;

    /**
     * 参数键值
     */
    @NotBlank(message = "参数键值不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 500, message = "参数键值长度不能超过500个字符")
    private String configValue;

    /**
     * 系统内置（Y是 N否）
     */
    private String configType;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
