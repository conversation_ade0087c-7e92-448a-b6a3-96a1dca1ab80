package com.oto.common.domain.model;

import lombok.Data;

import java.util.List;

/**
 * 网格配置
 *
 * <AUTHOR>
 */
@Data
public class GridConfig {
    
    /**
     * 总行数
     */
    private Integer totalRows;
    
    /**
     * 网格间距
     */
    private Integer gridGap;
    
    /**
     * 行配置列表
     */
    private List<RowConfig> rowConfigs;
    
    /**
     * 行配置
     */
    @Data
    public static class RowConfig {
        
        /**
         * 行索引（1-based）
         */
        private Integer rowIndex;
        
        /**
         * 列数
         */
        private Integer columnCount;
        
        /**
         * 行高度
         */
        private Integer rowHeight;
        
        /**
         * 排序顺序
         */
        private Integer sortOrder;
    }
}
