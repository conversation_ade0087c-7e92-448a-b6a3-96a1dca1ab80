package com.oto.common.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.oto.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 金刚位预览版本管理表 oto_diamond_preview_version
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("oto_diamond_preview_version")
public class OtoDiamondPreviewVersion extends TenantEntity {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 版本名称
     */
    @TableField("version_name")
    private String versionName;

    /**
     * 版本编码
     */
    @TableField("version_code")
    private String versionCode;

    /**
     * 网格配置JSON
     */
    @TableField("grid_config")
    private String gridConfig;

    /**
     * 位置配置JSON
     */
    @TableField("positions_config")
    private String positionsConfig;

    /**
     * 预览页面URL
     */
    @TableField("preview_url")
    private String previewUrl;

    /**
     * 状态：0-草稿，1-预览中，2-已发布，3-已回滚
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否当前版本：0-否，1-是
     */
    @TableField("is_current")
    private Integer isCurrent;

    /**
     * 发布时间
     */
    @TableField("publish_time")
    private LocalDateTime publishTime;

    /**
     * 停止时间（版本被替换的时间）
     */
    @TableField("stop_time")
    private LocalDateTime stopTime;

}
