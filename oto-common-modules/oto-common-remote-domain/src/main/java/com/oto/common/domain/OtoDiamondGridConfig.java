package com.oto.common.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.oto.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 金刚位网格配置表 oto_diamond_grid_config
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("oto_diamond_grid_config")
public class OtoDiamondGridConfig extends TenantEntity {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 网格行数（已废弃，使用totalRows）
     */
    @TableField("grid_rows")
    @Deprecated
    private Integer gridRows;

    /**
     * 网格列数（已废弃，使用行配置）
     */
    @TableField("grid_columns")
    @Deprecated
    private Integer gridColumns;

    /**
     * 总行数
     */
    @TableField("total_rows")
    private Integer totalRows;

    /**
     * 网格间距(px)
     */
    @TableField("grid_gap")
    private Integer gridGap;

    /**
     * 是否启用：0-禁用，1-启用
     */
    @TableField("is_active")
    private Integer isActive;

    /**
     * 配置描述
     */
    @TableField("description")
    private String description;

    /**
     * 行排序序号（决定行在页面上的显示顺序，不可重复）
     */
    @TableField("row_sort")
    private Integer rowSort;

    /**
     * 该行的列数
     */
    @TableField("column_count")
    private Integer columnCount;

    /**
     * 行高度(px)
     */
    @TableField("row_height")
    private Integer rowHeight;

}
