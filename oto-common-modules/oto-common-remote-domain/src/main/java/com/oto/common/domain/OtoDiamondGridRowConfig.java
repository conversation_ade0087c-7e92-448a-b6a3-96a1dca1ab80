package com.oto.common.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.oto.common.tenant.core.TenantEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 金刚位网格行配置表 oto_diamond_grid_row_config
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("oto_diamond_grid_row_config")
public class OtoDiamondGridRowConfig extends TenantEntity {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 网格配置ID（关联oto_diamond_grid_config）
     */
    @TableField("config_id")
    private Long configId;

    /**
     * 行索引（从1开始）
     */
    @TableField("row_index")
    private Integer rowIndex;

    /**
     * 该行的列数
     */
    @TableField("column_count")
    private Integer columnCount;

    /**
     * 行高度(px)，为空则使用默认
     */
    @TableField("row_height")
    private Integer rowHeight;

    /**
     * 排序序号
     */
    @TableField("sort_order")
    private Integer sortOrder;

}
