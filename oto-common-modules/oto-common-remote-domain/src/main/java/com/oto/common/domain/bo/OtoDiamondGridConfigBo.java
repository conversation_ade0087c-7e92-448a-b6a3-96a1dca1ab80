package com.oto.common.domain.bo;

import com.oto.common.core.validate.AddGroup;
import com.oto.common.core.validate.EditGroup;
import com.oto.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 金刚位网格配置业务对象 oto_diamond_grid_config
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = com.oto.common.domain.OtoDiamondGridConfig.class, reverseConvertGenerate = false)
public class OtoDiamondGridConfigBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 网格行数（已废弃，使用totalRows）
     */
    @Min(value = 1, message = "网格行数不能小于1")
    @Max(value = 10, message = "网格行数不能大于10")
    @Deprecated
    private Integer gridRows;

    /**
     * 网格列数（已废弃，使用行配置）
     */
    @Min(value = 1, message = "网格列数不能小于1")
    @Max(value = 8, message = "网格列数不能大于8")
    @Deprecated
    private Integer gridColumns;

    /**
     * 总行数（可选，系统会根据行配置自动计算）
     */
    @Min(value = 1, message = "总行数不能小于1")
    @Max(value = 10, message = "总行数不能大于10")
    private Integer totalRows;

    /**
     * 网格间距(px)
     */
    @Min(value = 8, message = "网格间距不能小于8px")
    @Max(value = 32, message = "网格间距不能大于32px")
    private Integer gridGap;

    /**
     * 是否启用：0-禁用，1-启用
     */
    private Integer isActive;

    /**
     * 配置描述
     */
    @Size(max = 200, message = "配置描述长度不能超过200个字符")
    private String description;

    /**
     * 行排序序号（决定行在页面上的显示顺序，不可重复）
     */
    @NotNull(message = "行排序序号不能为空", groups = { AddGroup.class, EditGroup.class })
    @Min(value = 1, message = "行排序序号不能小于1")
    @Max(value = 100, message = "行排序序号不能大于100")
    private Integer rowSort;

    /**
     * 该行的列数
     */
    @Min(value = 1, message = "列数不能小于1")
    @Max(value = 8, message = "列数不能大于8")
    private Integer columnCount;

    /**
     * 行高度(px)
     */
    @Min(value = 40, message = "行高度不能小于40px")
    @Max(value = 200, message = "行高度不能大于200px")
    private Integer rowHeight;

}
