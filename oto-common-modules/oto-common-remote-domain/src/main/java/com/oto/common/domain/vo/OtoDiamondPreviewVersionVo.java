package com.oto.common.domain.vo;


import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.oto.common.excel.annotation.ExcelDictFormat;
import com.oto.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 金刚位预览版本管理视图对象 oto_diamond_preview_version
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = com.oto.common.domain.OtoDiamondPreviewVersion.class)
public class OtoDiamondPreviewVersionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 版本名称
     */
    @ExcelProperty(value = "版本名称")
    private String versionName;

    /**
     * 版本编码
     */
    @ExcelProperty(value = "版本编码")
    private String versionCode;

    /**
     * 网格配置JSON
     */
    @ExcelProperty(value = "网格配置")
    private String gridConfig;

    /**
     * 位置配置JSON
     */
    @ExcelProperty(value = "位置配置")
    private String positionsConfig;

    /**
     * 预览页面URL
     */
    @ExcelProperty(value = "预览页面URL")
    private String previewUrl;

    /**
     * 状态：0-草稿，1-预览中，2-已发布，3-已回滚
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "diamond_preview_status")
    private Integer status;

    /**
     * 是否当前版本：0-否，1-是
     */
    @ExcelProperty(value = "是否当前版本", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private Integer isCurrent;

    /**
     * 发布时间
     */
    @ExcelProperty(value = "发布时间")
    private LocalDateTime publishTime;

    /**
     * 停止时间（版本被替换的时间）
     */
    @ExcelProperty(value = "停止时间")
    private LocalDateTime stopTime;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人")
    private String createBy;

    /**
     * 更新人
     */
    @ExcelProperty(value = "更新人")
    private String updateBy;

}
