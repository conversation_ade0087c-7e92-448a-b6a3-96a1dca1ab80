package com.oto.common.domain.bo;

import com.oto.common.core.validate.AddGroup;
import com.oto.common.core.validate.EditGroup;
import com.oto.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 金刚位配置业务对象 oto_diamond_position
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = com.oto.common.domain.OtoDiamondPosition.class, reverseConvertGenerate = false)
public class OtoDiamondPositionBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 金刚位名称
     */
    @NotBlank(message = "金刚位名称不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 100, message = "金刚位名称长度不能超过100个字符")
    private String name;

    /**
     * 图标内容（URL、SVG代码、Base64等）
     */
    private String icon;

    /**
     * 图标类型：url-链接地址，svg-SVG代码，base64-Base64编码，emoji-表情符号
     */
    @Size(max = 20, message = "图标类型长度不能超过20个字符")
    private String iconType;

    /**
     * 跳转链接
     */
    @Size(max = 500, message = "跳转链接长度不能超过500个字符")
    private String url;

    /**
     * 排序序号
     */
    @Min(value = 0, message = "排序序号不能小于0")
    private Integer sortOrder;

    /**
     * 状态：0-禁用，1-启用
     */
    @Min(value = 0, message = "状态值不能小于0")
    @Max(value = 1, message = "状态值不能大于1")
    private Integer status;

    /**
     * 描述
     */
    @Size(max = 500, message = "描述长度不能超过500个字符")
    private String description;

    /**
     * 网格行位置(1-based)
     */
    @Min(value = 1, message = "网格行位置不能小于1")
    private Integer gridRow;

    /**
     * 网格列位置(1-based)
     */
    @Min(value = 1, message = "网格列位置不能小于1")
    private Integer gridCol;

    /**
     * 是否已使用：0-未使用，1-已使用（已配置到网格中）
     */
    @Min(value = 0, message = "使用状态值不能小于0")
    @Max(value = 1, message = "使用状态值不能大于1")
    private Integer isUse;

}
