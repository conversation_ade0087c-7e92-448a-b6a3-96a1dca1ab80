package com.oto.common.domain.model;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 网格配置数据模型（对应唯一的网格配置）
 *
 * <AUTHOR>
 */
@Data
public class DiamondGridConfigModel {
    
    /**
     * 配置ID
     */
    private Long id;
    
    /**
     * 总行数
     */
    private Integer totalRows;
    
    /**
     * 网格间距(px)
     */
    private Integer gridGap;
    
    /**
     * 是否启用：0-禁用，1-启用
     */
    private Integer isActive;
    
    /**
     * 配置描述
     */
    private String description;
    
    /**
     * 行配置列表
     */
    private List<RowConfigModel> rowConfigs;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 行配置数据模型
     */
    @Data
    public static class RowConfigModel {
        
        /**
         * 行索引（从1开始）
         */
        private Integer rowIndex;
        
        /**
         * 该行的列数
         */
        private Integer columnCount;
        
        /**
         * 行高度(px)
         */
        private Integer rowHeight;
        
        /**
         * 排序序号
         */
        private Integer sortOrder;
    }
}
