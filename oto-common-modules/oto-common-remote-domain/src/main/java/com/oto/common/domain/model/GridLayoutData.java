package com.oto.common.domain.model;

import com.oto.common.domain.vo.OtoDiamondPositionVo;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 完整布局数据模型
 *
 * <AUTHOR>
 */
@Data
public class GridLayoutData {

    /**
     * 网格配置
     */
    private DiamondGridConfigModel gridConfig;

    /**
     * 金刚位列表
     */
    private List<OtoDiamondPositionVo> positions;

    /**
     * 位置矩阵（key: "row,col", value: 金刚位对象或null）
     */
    private Map<String, OtoDiamondPositionVo> positionMatrix;

    /**
     * 网格统计信息
     */
    private GridStatistics statistics;

    /**
     * 网格统计信息
     */
    @Data
    public static class GridStatistics {

        /**
         * 总位置数
         */
        private Integer totalPositions;

        /**
         * 已使用位置数
         */
        private Integer usedPositions;

        /**
         * 可用位置数
         */
        private Integer availablePositions;

        /**
         * 使用率
         */
        private Double usageRate;
    }
}
