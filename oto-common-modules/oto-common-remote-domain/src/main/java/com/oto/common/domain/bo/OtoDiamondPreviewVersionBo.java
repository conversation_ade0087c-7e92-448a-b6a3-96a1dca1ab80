package com.oto.common.domain.bo;

import com.oto.common.core.validate.AddGroup;
import com.oto.common.core.validate.EditGroup;
import com.oto.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 金刚位预览版本管理业务对象 oto_diamond_preview_version
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = com.oto.common.domain.OtoDiamondPreviewVersion.class, reverseConvertGenerate = false)
public class OtoDiamondPreviewVersionBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 版本名称
     */
    @NotBlank(message = "版本名称不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(max = 100, message = "版本名称长度不能超过100个字符")
    private String versionName;

    /**
     * 版本编码
     */
    @Size(max = 50, message = "版本编码长度不能超过50个字符")
    private String versionCode;

    /**
     * 网格配置JSON
     */
    @NotBlank(message = "网格配置不能为空", groups = { AddGroup.class, EditGroup.class })
    private String gridConfig;

    /**
     * 位置配置JSON
     */
    @NotBlank(message = "位置配置不能为空", groups = { AddGroup.class, EditGroup.class })
    private String positionsConfig;

    /**
     * 预览页面URL
     */
    @Size(max = 500, message = "预览页面URL长度不能超过500个字符")
    private String previewUrl;

    /**
     * 状态：0-草稿，1-预览中，2-已发布，3-已回滚
     */
    @Min(value = 0, message = "状态值不能小于0")
    @Max(value = 3, message = "状态值不能大于3")
    private Integer status;

    /**
     * 是否当前版本：0-否，1-是
     */
    private Integer isCurrent;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

}
