package com.oto.common.domain.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 通用分页响应对象
 * 用于前端服务与后管服务之间的数据传输
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@Data
public class PageResponse<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据列表
     */
    private List<T> rows;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 当前页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 总页数
     */
    private Integer pages;

    /**
     * 是否有下一页
     */
    private Boolean hasNext;

    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;

    /**
     * 是否为第一页
     */
    private Boolean isFirst;

    /**
     * 是否为最后一页
     */
    private Boolean isLast;

    /**
     * 构造方法
     */
    public PageResponse() {
    }

    /**
     * 构造方法
     *
     * @param rows     数据列表
     * @param total    总记录数
     * @param pageNum  当前页码
     * @param pageSize 每页大小
     */
    public PageResponse(List<T> rows, Long total, Integer pageNum, Integer pageSize) {
        this.rows = rows;
        this.total = total;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        
        // 计算总页数
        this.pages = (int) Math.ceil((double) total / pageSize);
        
        // 计算分页状态
        this.hasNext = pageNum < pages;
        this.hasPrevious = pageNum > 1;
        this.isFirst = pageNum == 1;
        this.isLast = pageNum.equals(pages);
    }

    /**
     * 创建分页响应对象
     *
     * @param rows     数据列表
     * @param total    总记录数
     * @param pageNum  当前页码
     * @param pageSize 每页大小
     * @param <T>      数据类型
     * @return 分页响应对象
     */
    public static <T> PageResponse<T> of(List<T> rows, Long total, Integer pageNum, Integer pageSize) {
        return new PageResponse<>(rows, total, pageNum, pageSize);
    }
}
