package com.oto.common.domain.vo;


import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.oto.common.excel.annotation.ExcelDictFormat;
import com.oto.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 金刚位网格配置视图对象 oto_diamond_grid_config
 *
 * <AUTHOR>
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = com.oto.common.domain.OtoDiamondGridConfig.class)
public class OtoDiamondGridConfigVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 网格行数（已废弃，使用totalRows）
     */
    @ExcelProperty(value = "网格行数")
    @Deprecated
    private Integer gridRows;

    /**
     * 网格列数（已废弃，使用行配置）
     */
    @ExcelProperty(value = "网格列数")
    @Deprecated
    private Integer gridColumns;

    /**
     * 总行数
     */
    @ExcelProperty(value = "总行数")
    private Integer totalRows;

    /**
     * 网格间距(px)
     */
    @ExcelProperty(value = "网格间距")
    private Integer gridGap;

    /**
     * 是否启用：0-禁用，1-启用
     */
    @ExcelProperty(value = "是否启用", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private Integer isActive;

    /**
     * 配置描述
     */
    @ExcelProperty(value = "配置描述")
    private String description;

    /**
     * 行排序序号（决定行在页面上的显示顺序，不可重复）
     */
    @ExcelProperty(value = "行排序序号")
    private Integer rowSort;

    /**
     * 该行的列数
     */
    @ExcelProperty(value = "列数")
    private Integer columnCount;

    /**
     * 行高度(px)
     */
    @ExcelProperty(value = "行高度")
    private Integer rowHeight;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 行配置列表（逐行配置信息）
     */
    private List<RowConfig> rowConfigs;

    /**
     * 行配置内部类
     */
    public static class RowConfig {
        /**
         * 行索引（从1开始）
         */
        private Integer rowIndex;

        /**
         * 该行的列数
         */
        private Integer columnCount;

        /**
         * 行高度(px)
         */
        private Integer rowHeight;

        /**
         * 排序序号
         */
        private Integer sortOrder;

        // Getters and Setters
        public Integer getRowIndex() {
            return rowIndex;
        }

        public void setRowIndex(Integer rowIndex) {
            this.rowIndex = rowIndex;
        }

        public Integer getColumnCount() {
            return columnCount;
        }

        public void setColumnCount(Integer columnCount) {
            this.columnCount = columnCount;
        }

        public Integer getRowHeight() {
            return rowHeight;
        }

        public void setRowHeight(Integer rowHeight) {
            this.rowHeight = rowHeight;
        }

        public Integer getSortOrder() {
            return sortOrder;
        }

        public void setSortOrder(Integer sortOrder) {
            this.sortOrder = sortOrder;
        }
    }

    /**
     * 获取网格摘要信息
     */
    public String getGridSummary() {
        if (rowConfigs == null || rowConfigs.isEmpty()) {
            return totalRows + "行（未配置列数）";
        }

        StringBuilder summary = new StringBuilder();
        summary.append(totalRows).append("行（");

        for (int i = 0; i < rowConfigs.size(); i++) {
            if (i > 0) summary.append("，");
            RowConfig row = rowConfigs.get(i);
            summary.append("第").append(row.getRowIndex()).append("行").append(row.getColumnCount()).append("列");
        }

        summary.append("）");
        return summary.toString();
    }

    /**
     * 获取总格子数
     */
    public Integer getTotalCells() {
        if (rowConfigs == null || rowConfigs.isEmpty()) {
            return 0;
        }

        return rowConfigs.stream()
                .mapToInt(RowConfig::getColumnCount)
                .sum();
    }

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人")
    private String createBy;

    /**
     * 更新人
     */
    @ExcelProperty(value = "更新人")
    private String updateBy;

}
