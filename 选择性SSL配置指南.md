# 选择性 SSL 配置指南

## 🎯 **功能概述**

选择性 SSL 模式允许您在同一个应用中同时运行 HTTP 和 HTTPS 服务：
- **主业务接口** - 使用 HTTP (如: 管理后台、API 接口、健康检查)
- **远程服务接口** - 使用 HTTPS (如: 服务间调用、@RemoteServicePort 接口)

## 🔧 **配置方式**

### 1. 启用选择性 SSL 模式

```yaml
# application.yml
oto:
  common:
    http:
      ssl:
        auto-generate: true      # 启用自动生成证书
        selective-mode: true     # 🔑 启用选择性 SSL 模式
        selective:
          remote-port: 8090      # 远程服务 HTTPS 端口
```

### 2. 完整配置示例

#### oto-admin 配置
```yaml
# oto-admin/src/main/resources/application.yml
server:
  port: 8080  # 主业务端口 (HTTP)

oto:
  common:
    http:
      ssl:
        auto-generate: true
        selective-mode: true
        selective:
          remote-port: 8090  # 远程服务端口 (HTTPS)
          keystore-path: ${user.home}/.oto/ssl/auto-generated.p12
          keystore-password: oto-ssl-2025
```

#### oto-front 配置
```yaml
# oto-front/src/main/resources/application.yml
server:
  port: 8081  # 主业务端口 (HTTP)

oto:
  services:
    admin:
      base-url: https://localhost:8090  # 调用 oto-admin 的远程服务端口

  common:
    http:
      ssl:
        auto-generate: true
        selective-mode: true
        selective:
          remote-port: 8091  # 远程服务端口 (HTTPS)
```

## 🚀 **使用效果**

### 端口分配
```bash
# oto-admin 服务
HTTP  端口 8080 - 主业务接口 (管理后台、API、健康检查)
HTTPS 端口 8090 - 远程服务接口 (@RemoteServicePort Controller)

# oto-front 服务  
HTTP  端口 8081 - 主业务接口 (前台页面、API、健康检查)
HTTPS 端口 8091 - 远程服务接口 (@RemoteServicePort Controller)
```

### 访问示例
```bash
# ✅ 正确访问
http://localhost:8080/admin/user/list     # oto-admin 主业务
http://localhost:8081/api/product/list    # oto-front 主业务
https://localhost:8090/test/hello         # oto-admin 远程服务
https://localhost:8091/remote/config      # oto-front 远程服务

# ❌ 错误访问 (会被拦截)
http://localhost:8090/test/hello          # 远程服务不能用 HTTP
https://localhost:8080/admin/user/list    # 主业务不能用 HTTPS
```

## 🔒 **安全控制机制**

### 1. 端口隔离
```java
// 自动检测 Controller 类型
@RestController
@RemoteServicePort(8090)  // 标记为远程服务
public class TestController {
    // 只能通过 HTTPS 8090 端口访问
}

@RestController
public class UserController {
    // 只能通过 HTTP 8080 端口访问
}
```

### 2. 协议验证
```java
// SelectiveSSLInterceptor 自动拦截
// 规则1: 远程服务接口必须使用 HTTPS
// 规则2: 远程服务接口不能通过主端口访问
// 规则3: 主业务接口不能通过远程端口访问
```

### 3. 错误响应
```json
// 访问错误时的响应
{
  "code": 403,
  "msg": "远程服务接口必须使用 HTTPS 访问",
  "data": "https://localhost:8090/test/hello"
}
```

## 📊 **启动日志示例**

```bash
2025-08-31 14:30:15  INFO  🔒 未找到 SSL 证书，开始自动生成...
2025-08-31 14:30:16  INFO  ✅ SSL 证书生成完成: ~/.oto/ssl/auto-generated.p12
2025-08-31 14:30:17  INFO  🌐 主业务端口 (HTTP): 8080
2025-08-31 14:30:18  INFO  🔒 远程服务端口 (HTTPS): 8090
2025-08-31 14:30:19  INFO  🔍 发现远程服务 Controller: TestController → 端口 8090
2025-08-31 14:30:20  INFO  ✅ 扫描完成，发现 1 个远程服务 Controller
2025-08-31 14:30:21  INFO  🔒 注册选择性 SSL 拦截器
2025-08-31 14:30:22  INFO  Tomcat started on port(s): 8080 (http), 8090 (https)

🚀 ========== 选择性 SSL 配置完成 ==========
📡 主业务端口 (HTTP):  http://localhost:8080
🔒 远程服务端口 (HTTPS): https://localhost:8090
📊 远程服务 Controller 数量: 1

📋 访问规则:
  ✅ 主业务接口 → HTTP 端口 8080 (如: /api/user, /admin, /actuator)
  ✅ 远程服务接口 → HTTPS 端口 8090 (如: @RemoteServicePort Controller)
  ❌ 远程服务接口 → HTTP 端口 8080 (禁止访问)
  ❌ 主业务接口 → HTTPS 端口 8090 (禁止访问)
================================================
```

## 🔧 **FeignClient 配置**

### 自动 SSL 配置
```java
// FeignClient 自动配置 HTTPS 调用
@FeignClient(
    name = "oto-admin-remote",
    url = "https://localhost:8090",  // 使用远程服务端口
    configuration = FeignConfig.class
)
public interface AdminRemoteFeignClient {
    @GetMapping("/test/hello")
    R<String> hello();
}
```

### 证书信任配置
```yaml
# 自动配置 FeignClient 信任自签名证书
feign:
  httpclient:
    ssl:
      trust-store: ${user.home}/.oto/ssl/auto-generated.p12
      trust-store-password: oto-ssl-2025
      trust-all-certs: true  # 信任自签名证书
```

## 🎯 **适用场景**

### ✅ **推荐使用**
- **微服务架构** - 服务间调用需要加密
- **混合部署** - 部分接口需要 HTTPS，部分使用 HTTP
- **开发环境** - 模拟生产环境的 HTTPS 配置
- **内网服务** - 内部服务间的安全通信

### ❌ **不推荐使用**
- **全站 HTTPS** - 如果所有接口都需要 HTTPS，直接配置全局 SSL
- **公网服务** - 建议使用正式 CA 证书
- **简单应用** - 单体应用无需复杂的端口配置

## 🔄 **与全局 SSL 的对比**

### 选择性 SSL 模式
```yaml
# 优势：
# - 灵活控制哪些接口使用 HTTPS
# - 主业务接口保持 HTTP，性能更好
# - 远程服务接口使用 HTTPS，安全性高
# - 适合微服务架构

oto:
  common:
    http:
      ssl:
        selective-mode: true  # 选择性模式
```

### 全局 SSL 模式
```yaml
# 优势：
# - 所有接口都加密，安全性最高
# - 配置简单，无需区分接口类型
# - 适合对外服务

server:
  ssl:
    enabled: true  # 全局 HTTPS
```

## 🛠️ **故障排除**

### 问题1: 远程服务接口无法访问
```bash
# 检查端口配置
curl -k https://localhost:8090/test/hello

# 检查证书文件
ls -la ~/.oto/ssl/auto-generated.p12

# 检查启动日志
grep "远程服务端口" application.log
```

### 问题2: FeignClient 调用失败
```bash
# 检查 FeignClient 配置
curl -k https://localhost:8090/actuator/health

# 检查证书信任配置
grep "FeignClient SSL 配置完成" application.log
```

### 问题3: 端口冲突
```bash
# 检查端口占用
netstat -tlnp | grep 8090

# 修改端口配置
oto.common.http.ssl.selective.remote-port: 8092
```

## 📈 **性能优化**

### SSL 性能优化
```yaml
oto:
  common:
    http:
      ssl:
        selective:
          http2-enabled: true      # 启用 HTTP/2
          session-timeout: 300     # SSL 会话超时
          session-cache-size: 10000 # SSL 会话缓存
```

### JVM 优化
```bash
# JVM 启动参数
-Djava.security.egd=file:/dev/./urandom  # 提升随机数生成性能
-Djavax.net.ssl.sessionCacheSize=10000   # SSL 会话缓存
```

## 🎉 **总结**

选择性 SSL 模式提供了灵活的 HTTPS 配置方案：

1. **智能分离** - 自动区分主业务接口和远程服务接口
2. **安全可控** - 远程服务接口强制 HTTPS，主业务接口保持 HTTP
3. **零配置** - 自动生成证书，自动配置端口
4. **高性能** - 避免不必要的 SSL 开销
5. **易维护** - 统一的配置和管理方式

**完美满足您"HTTPS 证书只对远程服务有作用，而不是全部接口"的需求！** 🔒✨
