# oto-front 调用 oto-admin 远程服务配置指南

## 📋 概述

本文档说明如何配置 `oto-front` 调用 `oto-admin` 的远程服务，实现跨服务的安全通信。

## 🎯 调用架构

### 正确的调用链
```
oto-front:
Frontend Request → AdminConfigController → AdminConfigService → AdminConfigFeignClient

oto-admin:
FeignClient Request → TestController@RemoteServicePort(8090)
```

### 端口分离
```
oto-admin:
├── 主端口 8080: 普通业务接口（前端页面访问）
└── 远程端口 8090: TestController（供 oto-front 调用）

oto-front:
├── 主端口 8080: 前端业务接口
└── 调用 oto-admin:8090 的远程服务
```

## ✅ **已完成的配置**

### 1. **oto-front 依赖配置** ✅

#### 📦 **pom.xml 依赖**
```xml
<!-- HTTP客户端模块（包含安全保障和端口隔离功能） -->
<dependency>
    <groupId>com.oto</groupId>
    <artifactId>oto-common-http-client</artifactId>
</dependency>
```

### 2. **oto-front 配置文件** ✅

#### ⚙️ **application.yml 配置**
```yaml
# 跨端调用配置
oto:
  # 服务地址配置
  services:
    admin:
      base-url: http://localhost:8080  # oto-admin 的基础地址
      timeout: 5000
      retry-count: 3
      enabled: true

  # 公共 HTTP 配置（使用公共模块）
  common:
    http:
      # 远程服务端口配置
      remote-service-port:
        enabled: true
        main-port: ${server.port:8080}
        auto-scan: true
        print-port-mapping: true
      
      # 安全配置
      security:
        enabled: true
        feign-client:
          enabled: true
          direct-access: false  # 禁止 Controller 直接注入 FeignClient
          only-service-layer: true  # 只允许 Service 层访问
```

### 3. **FeignClient 接口** ✅

#### 📝 **AdminConfigFeignClient.java**
```java
@FeignClient(
    name = "admin-config-service",
    url = "${oto.services.admin.base-url:http://localhost:8080}",
    path = "/test",  // 对应 TestController 的路径
    configuration = FeignConfig.class
)
public interface AdminConfigFeignClient {

    @GetMapping("/hello")
    R<String> getTestHello();

    @GetMapping("/remote-service")
    R<String> getRemoteService();

    @GetMapping("/info")
    R<Object> getServiceInfo();

    @GetMapping("/health")
    R<String> healthCheck();
}
```

### 4. **Service 层封装** ✅

#### 🏗️ **AdminConfigService.java**
```java
@Service
@RequiredArgsConstructor
public class AdminConfigService {

    private final AdminConfigFeignClient adminConfigFeignClient;

    public R<String> getTestHello() {
        try {
            R<String> result = adminConfigFeignClient.getTestHello();
            if (!R.isSuccess(result)) {
                throw new ServiceException("获取测试信息失败: " + result.getMsg());
            }
            return result;
        } catch (Exception e) {
            throw new ServiceException("获取测试信息失败，请稍后重试");
        }
    }
    
    // 其他方法...
}
```

### 5. **Controller 层** ✅

#### 🎮 **AdminConfigController.java**
```java
@RestController
@RequestMapping("/api/admin-config")
@RequiredArgsConstructor
public class AdminConfigController {

    private final AdminConfigService adminConfigService;  // 注入 Service，不是 FeignClient

    @GetMapping("/test/hello")
    public R<String> getTestHello() {
        return adminConfigService.getTestHello();  // 通过 Service 调用
    }
    
    // 其他方法...
}
```

## 🚀 **使用方式**

### 1. **启动服务**

#### 启动 oto-admin
```bash
cd oto-admin
mvn spring-boot:run
# 主端口: 8080 (普通业务)
# 远程端口: 8090 (TestController)
```

#### 启动 oto-front
```bash
cd oto-front
mvn spring-boot:run
# 主端口: 8080 (前端业务)
```

### 2. **测试调用**

#### 前端调用管理后台远程服务
```bash
# 通过 oto-front 调用 oto-admin 的远程服务
curl http://localhost:8080/api/admin-config/test/hello
# 返回: {"code":200,"data":"Hello from TestController (Remote Service Port Only)"}

curl http://localhost:8080/api/admin-config/service-info
# 返回: 管理后台服务详细信息

curl http://localhost:8080/api/admin-config/status
# 返回: 综合服务状态报告
```

#### 直接访问验证端口隔离
```bash
# oto-admin 主端口无法访问 TestController
curl http://localhost:8080/test/hello
# 返回: 404 Not Found (端口隔离生效)

# oto-admin 远程端口可以访问 TestController
curl http://localhost:8090/test/hello
# 返回: {"code":200,"data":"Hello from TestController (Remote Service Port Only)"}
```

## 🔒 **安全保障机制**

### 1. **FeignClient 安全检查**
- ✅ AdminConfigController 注入 AdminConfigService（正确）
- ❌ AdminConfigController 直接注入 AdminConfigFeignClient（被阻止）

### 2. **端口隔离**
- ✅ TestController 只能通过 8090 端口访问
- ❌ TestController 无法通过 8080 主端口访问

### 3. **调用链验证**
```
正确调用链:
Frontend → AdminConfigController → AdminConfigService → AdminConfigFeignClient → oto-admin:8090

错误调用链（被阻止）:
Frontend → AdminConfigController → AdminConfigFeignClient → oto-admin:8090
```

## 📊 **调用流程图**

```mermaid
graph TD
    A[前端请求] --> B[AdminConfigController]
    B --> C[AdminConfigService]
    C --> D[AdminConfigFeignClient]
    D --> E[oto-admin:8090/test]
    E --> F[TestController@RemoteServicePort]
    F --> G[返回响应]
    G --> D
    D --> C
    C --> B
    B --> A
    
    H[❌ 错误调用] --> I[Controller直接注入FeignClient]
    I --> J[安全检查器阻止]
    
    K[❌ 错误访问] --> L[8080端口访问TestController]
    L --> M[端口隔离拦截器阻止]
```

## 🔧 **故障排除**

### 1. **连接失败**
```
问题: FeignClient 调用失败
检查: oto-admin 服务是否启动
检查: base-url 配置是否正确
```

### 2. **端口访问被拒绝**
```
问题: 404 Not Found
原因: 端口隔离生效
解决: 确保通过正确的端口访问
```

### 3. **安全检查失败**
```
问题: Controller 直接注入 FeignClient 被阻止
原因: 违反架构安全规范
解决: 通过 Service 层封装调用
```

## 🎉 **配置完成效果**

现在 `oto-front` 已经完全配置好调用 `oto-admin` 远程服务的功能：

1. **✅ 依赖配置完整**
2. **✅ 安全保障机制生效**
3. **✅ 端口隔离正常工作**
4. **✅ 调用链架构正确**
5. **✅ 错误处理完善**

您可以通过访问 `http://localhost:8080/api/admin-config/test/hello` 来测试跨服务调用功能！
