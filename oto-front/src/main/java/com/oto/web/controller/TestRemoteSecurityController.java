package com.oto.web.controller;

import com.oto.common.core.domain.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Sa-Token Same-Token 测试控制器
 * 用于测试 Sa-Token Same-Token 服务间认证功能
 *
 * <AUTHOR>
 * @date 2025-08-30
 */
@Slf4j
@RestController
@RequestMapping("/app/api/test")
public class TestRemoteSecurityController {

    /**
     * 测试远程调用安全验证
     * 这个接口会被远程调用安全模块保护
     */
    @GetMapping("/security")
    public R<Map<String, Object>> testSecurity() {
        log.info("远程调用安全验证测试接口被调用");
        
        Map<String, Object> result = new HashMap<>();
        result.put("message", "远程调用安全验证通过");
        result.put("timestamp", LocalDateTime.now());
        result.put("service", "oto-front");
        result.put("endpoint", "/app/api/test/security");
        
        return R.ok(result);
    }

    /**
     * 健康检查接口
     * 用于测试服务间调用
     */
    @GetMapping("/health")
    public R<Map<String, Object>> health() {
        log.info("健康检查接口被调用");
        
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("timestamp", LocalDateTime.now());
        result.put("service", "oto-front");
        
        return R.ok(result);
    }

    /**
     * 获取会员服务信息
     */
    @GetMapping("/info")
    public R<Map<String, Object>> getServiceInfo() {
        log.info("会员服务信息接口被调用");
        
        Map<String, Object> result = new HashMap<>();
        result.put("serviceName", "oto-front");
        result.put("version", "1.0.0");
        result.put("description", "OTO会员服务");
        result.put("timestamp", LocalDateTime.now());
        
        return R.ok(result);
    }
}
