-- 金刚位配置系统测试数据
-- 用于演示和测试增强功能

-- 清理现有测试数据（可选）
-- DELETE FROM oto_diamond_position WHERE name LIKE '测试%';

-- 插入测试金刚位数据
INSERT INTO oto_diamond_position (name, icon, icon_type, url, sort_order, grid_row, grid_col, span_rows, span_cols, status, is_use, description, create_by, create_time, update_by, update_time) VALUES
-- 已放置的金刚位（第1行）
('测试应用1', '🏠', 'emoji', 'https://example.com/app1', 1, 1, 1, 1, 1, 1, 1, '首页应用，用于快速访问主要功能', 'admin', NOW(), 'admin', NOW()),
('测试应用2', '📊', 'emoji', 'https://example.com/app2', 2, 1, 2, 1, 1, 1, 1, '数据分析应用，查看各种统计报表', 'admin', NOW(), 'admin', NOW()),
('测试应用3', '💬', 'emoji', 'https://example.com/app3', 3, 1, 3, 1, 1, 1, 1, '消息中心，处理各种通知和消息', 'admin', NOW(), 'admin', NOW()),
('测试应用4', '⚙️', 'emoji', 'https://example.com/app4', 4, 1, 4, 1, 1, 1, 1, '系统设置，配置各种参数', 'admin', NOW(), 'admin', NOW()),

-- 已放置的金刚位（第2行）
('测试应用5', '📝', 'emoji', 'https://example.com/app5', 5, 2, 1, 1, 1, 1, 1, '文档管理，创建和编辑文档', 'admin', NOW(), 'admin', NOW()),
('测试应用6', '👥', 'emoji', 'https://example.com/app6', 6, 2, 2, 1, 1, 1, 1, '用户管理，管理系统用户', 'admin', NOW(), 'admin', NOW()),

-- 未放置的金刚位（待分配）
('测试应用7', '📅', 'emoji', 'https://example.com/app7', 7, NULL, NULL, 1, 1, 1, 0, '日历应用，管理日程安排', 'admin', NOW(), 'admin', NOW()),
('测试应用8', '📧', 'emoji', 'https://example.com/app8', 8, NULL, NULL, 1, 1, 1, 0, '邮件系统，发送和接收邮件', 'admin', NOW(), 'admin', NOW()),
('测试应用9', '🔍', 'emoji', 'https://example.com/app9', 9, NULL, NULL, 1, 1, 1, 0, '搜索功能，全局搜索各种内容', 'admin', NOW(), 'admin', NOW()),
('测试应用10', '📈', 'emoji', 'https://example.com/app10', 10, NULL, NULL, 1, 1, 1, 0, '业务分析，查看业务数据', 'admin', NOW(), 'admin', NOW()),
('测试应用11', '🛡️', 'emoji', 'https://example.com/app11', 11, NULL, NULL, 1, 1, 1, 0, '安全中心，管理安全策略', 'admin', NOW(), 'admin', NOW()),
('测试应用12', '📱', 'emoji', 'https://example.com/app12', 12, NULL, NULL, 1, 1, 1, 0, '移动应用，手机端功能', 'admin', NOW(), 'admin', NOW()),

-- 禁用状态的金刚位（用于测试过滤）
('测试应用13', '🚫', 'emoji', 'https://example.com/app13', 13, NULL, NULL, 1, 1, 0, 0, '已禁用的应用，不会显示在列表中', 'admin', NOW(), 'admin', NOW()),

-- 使用SVG图标的金刚位
('SVG图标应用', '<svg viewBox="0 0 24 24" fill="currentColor"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>', 'svg', 'https://example.com/svg-app', 14, NULL, NULL, 1, 1, 1, 0, '使用SVG图标的应用示例', 'admin', NOW(), 'admin', NOW()),

-- 使用URL图标的金刚位
('URL图标应用', 'https://via.placeholder.com/64x64/4CAF50/FFFFFF?text=URL', 'url', 'https://example.com/url-app', 15, NULL, NULL, 1, 1, 1, 0, '使用URL图标的应用示例', 'admin', NOW(), 'admin', NOW());

-- 验证插入的数据
SELECT 
    '插入测试数据完成' as status,
    COUNT(*) as total_count,
    SUM(CASE WHEN is_use = 1 THEN 1 ELSE 0 END) as used_count,
    SUM(CASE WHEN is_use = 0 THEN 1 ELSE 0 END) as unused_count,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as enabled_count,
    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as disabled_count
FROM oto_diamond_position 
WHERE name LIKE '测试%' OR name LIKE '%图标应用';

-- 显示网格布局情况
SELECT 
    CONCAT('第', grid_row, '行第', grid_col, '列') as position,
    name,
    icon,
    icon_type,
    description
FROM oto_diamond_position 
WHERE grid_row IS NOT NULL AND grid_col IS NOT NULL
ORDER BY grid_row, grid_col;

-- 显示未分配的金刚位
SELECT 
    name,
    icon,
    icon_type,
    description
FROM oto_diamond_position 
WHERE is_use = 0 AND status = 1
ORDER BY sort_order;
