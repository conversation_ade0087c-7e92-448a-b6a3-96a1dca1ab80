-- =====================================================
-- 金刚位配置系统 - 添加 is_use 字段迁移脚本
-- 执行时间：2025-01-27
-- 说明：为 oto_diamond_position 表添加 is_use 字段
-- =====================================================

-- 1. 检查字段是否已存在（避免重复执行）
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'oto_diamond_position'
    AND COLUMN_NAME = 'is_use'
);

-- 2. 如果字段不存在，则添加字段
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE oto_diamond_position ADD COLUMN is_use tinyint(1) DEFAULT 0 COMMENT ''是否已使用：0-未使用，1-已使用（已配置到网格中）''',
    'SELECT ''字段 is_use 已存在，跳过添加'' AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 更新现有数据：如果有网格位置信息，则标记为已使用
UPDATE oto_diamond_position 
SET is_use = 1 
WHERE grid_row IS NOT NULL AND grid_col IS NOT NULL AND is_use = 0;

-- 4. 检查索引是否已存在，如果不存在则创建
SET @index_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'oto_diamond_position'
    AND INDEX_NAME = 'idx_diamond_position_is_use'
);

SET @sql = IF(@index_exists = 0,
    'CREATE INDEX idx_diamond_position_is_use ON oto_diamond_position(is_use)',
    'SELECT ''索引 idx_diamond_position_is_use 已存在，跳过创建'' AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 检查复合索引是否已存在，如果不存在则创建
SET @index_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'oto_diamond_position'
    AND INDEX_NAME = 'idx_diamond_position_grid_position'
);

SET @sql = IF(@index_exists = 0,
    'CREATE INDEX idx_diamond_position_grid_position ON oto_diamond_position(grid_row, grid_col)',
    'SELECT ''索引 idx_diamond_position_grid_position 已存在，跳过创建'' AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. 验证迁移结果
SELECT 
    '迁移完成' as status,
    COUNT(*) as total_positions,
    SUM(CASE WHEN is_use = 1 THEN 1 ELSE 0 END) as used_positions,
    SUM(CASE WHEN is_use = 0 THEN 1 ELSE 0 END) as unused_positions,
    SUM(CASE WHEN grid_row IS NOT NULL AND grid_col IS NOT NULL THEN 1 ELSE 0 END) as has_grid_position
FROM oto_diamond_position;

-- 7. 显示表结构确认
DESCRIBE oto_diamond_position;

-- 8. 显示索引信息
SHOW INDEX FROM oto_diamond_position WHERE Key_name IN ('idx_diamond_position_is_use', 'idx_diamond_position_grid_position');

SELECT 'is_use 字段迁移完成！' as final_message;
