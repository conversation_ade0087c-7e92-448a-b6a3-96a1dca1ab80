-- 简化版本：添加 is_use 字段到 oto_diamond_position 表

-- 1. 添加 is_use 字段
ALTER TABLE oto_diamond_position 
ADD COLUMN is_use tinyint(1) DEFAULT 0 COMMENT '是否已使用：0-未使用，1-已使用（已配置到网格中）';

-- 2. 更新现有数据：如果有网格位置信息，则标记为已使用
UPDATE oto_diamond_position 
SET is_use = 1 
WHERE grid_row IS NOT NULL AND grid_col IS NOT NULL;

-- 3. 添加索引以提高查询性能
CREATE INDEX idx_diamond_position_is_use ON oto_diamond_position(is_use);
CREATE INDEX idx_diamond_position_grid_position ON oto_diamond_position(grid_row, grid_col);

-- 4. 验证结果
SELECT 
    COUNT(*) as total_count,
    SUM(CASE WHEN is_use = 1 THEN 1 ELSE 0 END) as used_count,
    SUM(CASE WHEN is_use = 0 THEN 1 ELSE 0 END) as unused_count
FROM oto_diamond_position;
