-- 金刚位配置系统增强 - 数据库迁移脚本
-- 执行时间：2025-01-27

-- 1. 为 oto_diamond_position 表添加 is_use 字段
ALTER TABLE oto_diamond_position 
ADD COLUMN is_use tinyint(1) DEFAULT 0 COMMENT '是否已使用：0-未使用，1-已使用（已配置到网格中）';

-- 2. 更新现有数据：如果有网格位置信息，则标记为已使用
UPDATE oto_diamond_position 
SET is_use = 1 
WHERE grid_row IS NOT NULL AND grid_col IS NOT NULL;

-- 3. 添加索引以提高查询性能
CREATE INDEX idx_diamond_position_is_use ON oto_diamond_position(is_use);
CREATE INDEX idx_diamond_position_grid_position ON oto_diamond_position(grid_row, grid_col);

-- 4. 添加菜单权限数据
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('金刚位布局管理', (SELECT menu_id FROM sys_menu WHERE menu_name = '金刚位配置' LIMIT 1), 2, 'diamond-layout', 'otoconfig/diamond-position/layout', '', 1, 0, 'C', '0', '0', 'otoconfig:diamond-layout:view', 'grid', 'admin', NOW(), '', NULL, '金刚位网格布局管理页面');

-- 5. 添加布局管理相关权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, query_param, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('获取布局数据', (SELECT menu_id FROM sys_menu WHERE perms = 'otoconfig:diamond-layout:view' LIMIT 1), 1, '', '', '', 1, 0, 'F', '0', '0', 'otoconfig:diamond-layout:list', '#', 'admin', NOW(), '', NULL, ''),
('更新位置', (SELECT menu_id FROM sys_menu WHERE perms = 'otoconfig:diamond-layout:view' LIMIT 1), 2, '', '', '', 1, 0, 'F', '0', '0', 'otoconfig:diamond-position:update-positions', '#', 'admin', NOW(), '', NULL, ''),
('检测冲突', (SELECT menu_id FROM sys_menu WHERE perms = 'otoconfig:diamond-layout:view' LIMIT 1), 3, '', '', '', 1, 0, 'F', '0', '0', 'otoconfig:diamond-position:check-conflicts', '#', 'admin', NOW(), '', NULL, ''),
('自动分配', (SELECT menu_id FROM sys_menu WHERE perms = 'otoconfig:diamond-layout:view' LIMIT 1), 4, '', '', '', 1, 0, 'F', '0', '0', 'otoconfig:diamond-position:auto-assign', '#', 'admin', NOW(), '', NULL, ''),
('清空位置', (SELECT menu_id FROM sys_menu WHERE perms = 'otoconfig:diamond-layout:view' LIMIT 1), 5, '', '', '', 1, 0, 'F', '0', '0', 'otoconfig:diamond-position:clear-position', '#', 'admin', NOW(), '', NULL, '');

-- 6. 创建系统配置表记录（用于存储网格配置）
INSERT INTO sys_config (config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark) VALUES
('金刚位网格配置', 'oto.diamond.grid.config', '{"id":1,"totalRows":6,"gridGap":16,"isActive":1,"description":"默认网格配置：6行4列，适配所有设备","rowConfigs":[{"rowIndex":1,"columnCount":4,"rowHeight":80,"sortOrder":1},{"rowIndex":2,"columnCount":4,"rowHeight":80,"sortOrder":2},{"rowIndex":3,"columnCount":4,"rowHeight":80,"sortOrder":3},{"rowIndex":4,"columnCount":4,"rowHeight":80,"sortOrder":4},{"rowIndex":5,"columnCount":4,"rowHeight":80,"sortOrder":5},{"rowIndex":6,"columnCount":4,"rowHeight":80,"sortOrder":6}]}', 'Y', 'admin', NOW(), '', NULL, '金刚位网格布局配置信息')
ON DUPLICATE KEY UPDATE 
config_value = VALUES(config_value),
update_time = NOW();

-- 7. 验证数据完整性
SELECT 
    COUNT(*) as total_positions,
    SUM(CASE WHEN is_use = 1 THEN 1 ELSE 0 END) as used_positions,
    SUM(CASE WHEN is_use = 0 THEN 1 ELSE 0 END) as unused_positions,
    SUM(CASE WHEN grid_row IS NOT NULL AND grid_col IS NOT NULL THEN 1 ELSE 0 END) as has_grid_position
FROM oto_diamond_position;

-- 8. 显示迁移完成信息
SELECT 'Diamond Position Enhancement Migration Completed Successfully!' as status;
