# 🚀 OTO系统 REST HTTP 通信改造方案 v1.0

## 📋 文档概述

**文档版本**: v1.0  
**创建日期**: 2025-08-30  
**适用范围**: OTO后管应用与会员系统应用间通信改造  
**技术栈**: Spring Boot 3.x + RestTemplate/WebClient + 自定义安全认证

---

## 🎯 改造目标

### 现状问题
- ❌ 两个应用直接通过数据库通信，安全性差
- ❌ 业务逻辑耦合严重，维护困难
- ❌ 数据一致性难以保证
- ❌ 缺乏统一的接口规范和监控

### 改造目标
- ✅ 建立标准化的 REST API 通信机制
- ✅ 实现服务间安全认证和授权
- ✅ 完善接口调用监控和日志记录
- ✅ 提升系统可维护性和扩展性

---

## 🏗️ 总体架构设计

### 改造前架构
```
┌─────────────────┐    直接数据库访问    ┌─────────────────┐
│   OTO-Admin     │◄─────────────────►│   OTO-Front     │
│   (后管应用)     │                    │   (会员应用)     │
│                 │                    │                 │
│ ┌─────────────┐ │                    │ ┌─────────────┐ │
│ │ Admin       │ │                    │ │ Member      │ │
│ │ Service     │ │                    │ │ Service     │ │
│ └─────────────┘ │                    │ └─────────────┘ │
│ ┌─────────────┐ │                    │ ┌─────────────┐ │
│ │ Admin DB    │ │◄──────────────────►│ │ Member DB   │ │
│ │ (master)    │ │                    │ │ (master2)   │ │
│ └─────────────┘ │                    │ └─────────────┘ │
└─────────────────┘                    └─────────────────┘
```

### 改造后架构
```
┌─────────────────┐    REST HTTP API    ┌─────────────────┐
│   OTO-Admin     │◄──────────────────►│   OTO-Front     │
│   (后管应用)     │                    │   (会员应用)     │
│                 │                    │                 │
│ ┌─────────────┐ │                    │ ┌─────────────┐ │
│ │ Admin API   │ │                    │ │ Member API  │ │
│ │ Controller  │ │                    │ │ Controller  │ │
│ └─────────────┘ │                    │ └─────────────┘ │
│ ┌─────────────┐ │                    │ ┌─────────────┐ │
│ │ HTTP Client │ │                    │ │ HTTP Client │ │
│ │ Service     │ │                    │ │ Service     │ │
│ └─────────────┘ │                    │ └─────────────┘ │
│ ┌─────────────┐ │                    │ ┌─────────────┐ │
│ │ Admin       │ │                    │ │ Member      │ │
│ │ Service     │ │                    │ │ Service     │ │
│ └─────────────┘ │                    │ └─────────────┘ │
│ ┌─────────────┐ │                    │ ┌─────────────┐ │
│ │ Admin DB    │ │                    │ │ Member DB   │ │
│ │ (master)    │ │                    │ │ (master2)   │ │
│ └─────────────┘ │                    │ └─────────────┘ │
└─────────────────┘                    └─────────────────┘
```

---

## 📦 模块设计

### 1. 通信基础模块 (oto-common-http-client)

**目录结构**:
```
oto-common-modules/oto-common-http-client/
├── pom.xml
└── src/main/java/com/oto/common/http/
    ├── client/
    │   ├── AdminServiceClient.java      # 后管服务客户端
    │   ├── MemberServiceClient.java     # 会员服务客户端
    │   └── BaseHttpClient.java          # 基础HTTP客户端
    ├── config/
    │   ├── HttpClientConfig.java        # HTTP客户端配置
    │   └── ServiceUrlConfig.java        # 服务地址配置
    ├── interceptor/
    │   ├── AuthInterceptor.java         # 认证拦截器
    │   └── LoggingInterceptor.java      # 日志拦截器
    ├── dto/
    │   ├── AdminUserDTO.java            # 后管用户DTO
    │   ├── MemberDTO.java               # 会员DTO
    │   └── BaseResponseDTO.java         # 基础响应DTO
    └── exception/
        └── ServiceCallException.java    # 服务调用异常
```

### 2. API 控制器模块

**后管应用 API 控制器**:
```
oto-admin-modules/oto-admin-system/
└── src/main/java/com/oto/system/controller/api/
    ├── AdminUserApiController.java     # 用户API
    ├── AdminConfigApiController.java   # 配置API
    └── AdminRoleApiController.java     # 角色API
```

**会员应用 API 控制器**:
```
oto-front-modules/oto-front-system/
└── src/main/java/com/oto/system/controller/api/
    ├── MemberApiController.java        # 会员API
    ├── MemberOrderApiController.java   # 订单API
    └── MemberConfigApiController.java  # 配置API
```

---

## 🔧 技术实现方案

### 1. HTTP 客户端配置

**依赖配置 (pom.xml)**:
```xml
<dependencies>
    <!-- Spring Boot Web -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    
    <!-- WebClient (推荐) -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-webflux</artifactId>
    </dependency>
    
    <!-- Apache HttpClient (备选) -->
    <dependency>
        <groupId>org.apache.httpcomponents.client5</groupId>
        <artifactId>httpclient5</artifactId>
    </dependency>
    
    <!-- 通信监控模块 -->
    <dependency>
        <groupId>com.oto</groupId>
        <artifactId>oto-front-common-mapper</artifactId>
    </dependency>
</dependencies>
```

**HTTP 客户端配置类**:
```java
@Configuration
@EnableConfigurationProperties(ServiceUrlConfig.class)
public class HttpClientConfig {
    
    @Bean
    @Primary
    public WebClient webClient(ServiceUrlConfig config) {
        return WebClient.builder()
            .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024))
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .filter(authFilter())
            .filter(loggingFilter())
            .build();
    }
    
    @Bean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.getInterceptors().add(new AuthInterceptor());
        restTemplate.getInterceptors().add(new LoggingInterceptor());
        return restTemplate;
    }
    
    private ExchangeFilterFunction authFilter() {
        return ExchangeFilterFunction.ofRequestProcessor(request -> {
            return Mono.just(ClientRequest.from(request)
                .header("X-Service-Key", "your-service-api-key")
                .header("X-Service-Name", "oto-front")
                .build());
        });
    }
    
    private ExchangeFilterFunction loggingFilter() {
        return ExchangeFilterFunction.ofRequestProcessor(request -> {
            // 记录请求日志
            log.info("HTTP Request: {} {}", request.method(), request.url());
            return Mono.just(request);
        });
    }
}
```

### 2. 服务地址配置

**配置文件 (application.yml)**:
```yaml
# 服务地址配置
oto:
  services:
    admin:
      base-url: http://localhost:8080
      timeout: 5000
      retry-count: 3
    member:
      base-url: http://localhost:8081  
      timeout: 5000
      retry-count: 3
      
# 安全配置
security:
  service:
    api-key: "oto-service-secret-key-2025"
    enabled: true
```

**配置类**:
```java
@ConfigurationProperties(prefix = "oto.services")
@Data
public class ServiceUrlConfig {
    
    private ServiceConfig admin;
    private ServiceConfig member;
    
    @Data
    public static class ServiceConfig {
        private String baseUrl;
        private Integer timeout = 5000;
        private Integer retryCount = 3;
    }
}
```

---

## 🔒 安全认证方案

### 1. API Key 认证

**认证拦截器**:
```java
@Component
public class AuthInterceptor implements ClientHttpRequestInterceptor {
    
    @Value("${security.service.api-key}")
    private String apiKey;
    
    @Override
    public ClientHttpResponse intercept(
            HttpRequest request, 
            byte[] body, 
            ClientHttpRequestExecution execution) throws IOException {
        
        // 添加认证头
        request.getHeaders().add("X-Service-Key", apiKey);
        request.getHeaders().add("X-Service-Name", getServiceName());
        request.getHeaders().add("X-Request-Time", String.valueOf(System.currentTimeMillis()));
        
        return execution.execute(request, body);
    }
    
    private String getServiceName() {
        // 根据当前应用返回服务名
        return "oto-front"; // 或 "oto-admin"
    }
}
```

**服务端验证**:
```java
@Component
public class ServiceAuthFilter implements Filter {
    
    @Value("${security.service.api-key}")
    private String expectedApiKey;
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, 
                        FilterChain chain) throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        
        // 只对 /api/** 路径进行验证
        if (!httpRequest.getRequestURI().startsWith("/admin/api") && 
            !httpRequest.getRequestURI().startsWith("/app/api")) {
            chain.doFilter(request, response);
            return;
        }
        
        String apiKey = httpRequest.getHeader("X-Service-Key");
        String serviceName = httpRequest.getHeader("X-Service-Name");
        
        if (!expectedApiKey.equals(apiKey)) {
            ((HttpServletResponse) response).setStatus(HttpStatus.UNAUTHORIZED.value());
            return;
        }
        
        // 验证通过，继续处理
        chain.doFilter(request, response);
    }
}
```

### 2. Token 传递机制

**Token 传递拦截器**:
```java
@Component
public class TokenRelayInterceptor implements ClientHttpRequestInterceptor {
    
    @Override
    public ClientHttpResponse intercept(
            HttpRequest request, 
            byte[] body, 
            ClientHttpRequestExecution execution) throws IOException {
        
        // 获取当前用户Token
        try {
            String token = StpUtil.getTokenValue();
            if (StrUtil.isNotBlank(token)) {
                request.getHeaders().add("Authorization", token);
                request.getHeaders().add("X-User-Id", StpUtil.getLoginIdAsString());
            }
        } catch (Exception e) {
            // Token 不存在时忽略
        }
        
        return execution.execute(request, body);
    }
}
```

---

## 📡 服务客户端实现

### 1. 后管服务客户端

```java
@Service
@Slf4j
public class AdminServiceClient {
    
    @Autowired
    private WebClient webClient;
    
    @Autowired
    private ServiceUrlConfig serviceConfig;
    
    /**
     * 根据用户ID获取用户信息
     */
    public AdminUserDTO getUserById(Long userId) {
        AppCommunicationDTO commDto = AppCommunicationUtil.createCommunicationDTO(
            "oto-front", "oto-admin", "HTTP_API"
        );
        commDto.setMethod("GET");
        commDto.setPath("/admin/api/user/" + userId);
        
        AppCommunicationUtil.markStart(commDto);
        
        try {
            AdminUserDTO result = webClient.get()
                .uri(serviceConfig.getAdmin().getBaseUrl() + "/admin/api/user/{userId}", userId)
                .retrieve()
                .onStatus(HttpStatusCode::isError, response -> {
                    return response.bodyToMono(String.class)
                        .flatMap(error -> Mono.error(new ServiceCallException("获取用户信息失败: " + error)));
                })
                .bodyToMono(new ParameterizedTypeReference<R<AdminUserDTO>>() {})
                .map(R::getData)
                .block(Duration.ofMillis(serviceConfig.getAdmin().getTimeout()));
            
            AppCommunicationUtil.markSuccess(commDto, result);
            return result;
            
        } catch (Exception e) {
            AppCommunicationUtil.markFailure(commDto, 500, e.getMessage());
            throw new ServiceCallException("调用后管服务失败", e);
        } finally {
            // 保存通信日志
            saveCommLog(commDto);
        }
    }
    
    /**
     * 批量获取用户信息
     */
    public List<AdminUserDTO> getUsersByIds(List<Long> userIds) {
        AppCommunicationDTO commDto = AppCommunicationUtil.createCommunicationDTO(
            "oto-front", "oto-admin", "HTTP_API"
        );
        commDto.setMethod("POST");
        commDto.setPath("/admin/api/user/batch");
        commDto.setRequestData(userIds);
        
        AppCommunicationUtil.markStart(commDto);
        
        try {
            List<AdminUserDTO> result = webClient.post()
                .uri(serviceConfig.getAdmin().getBaseUrl() + "/admin/api/user/batch")
                .bodyValue(userIds)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<R<List<AdminUserDTO>>>() {})
                .map(R::getData)
                .block(Duration.ofMillis(serviceConfig.getAdmin().getTimeout()));
            
            AppCommunicationUtil.markSuccess(commDto, result);
            return result;
            
        } catch (Exception e) {
            AppCommunicationUtil.markFailure(commDto, 500, e.getMessage());
            throw new ServiceCallException("批量获取用户信息失败", e);
        } finally {
            saveCommLog(commDto);
        }
    }
    
    /**
     * 获取系统配置
     */
    public String getConfigValue(String configKey) {
        // 实现类似上面的逻辑
        // ...
    }
    
    private void saveCommLog(AppCommunicationDTO dto) {
        try {
            // 异步保存通信日志
            CompletableFuture.runAsync(() -> {
                AppCommunicationLog log = AppCommunicationUtil.convertToEntity(dto);
                // 保存到数据库
                // communicationLogService.save(log);
            });
        } catch (Exception e) {
            log.warn("保存通信日志失败", e);
        }
    }
}
```

### 2. 会员服务客户端

```java
@Service
@Slf4j
public class MemberServiceClient {
    
    @Autowired
    private WebClient webClient;
    
    @Autowired
    private ServiceUrlConfig serviceConfig;
    
    /**
     * 根据会员ID获取会员信息
     */
    public MemberDTO getMemberById(Long memberId) {
        AppCommunicationDTO commDto = AppCommunicationUtil.createCommunicationDTO(
            "oto-admin", "oto-front", "HTTP_API"
        );
        commDto.setMethod("GET");
        commDto.setPath("/app/api/member/" + memberId);
        
        AppCommunicationUtil.markStart(commDto);
        
        try {
            MemberDTO result = webClient.get()
                .uri(serviceConfig.getMember().getBaseUrl() + "/app/api/member/{memberId}", memberId)
                .retrieve()
                .onStatus(HttpStatusCode::isError, response -> {
                    return response.bodyToMono(String.class)
                        .flatMap(error -> Mono.error(new ServiceCallException("获取会员信息失败: " + error)));
                })
                .bodyToMono(new ParameterizedTypeReference<R<MemberDTO>>() {})
                .map(R::getData)
                .block(Duration.ofMillis(serviceConfig.getMember().getTimeout()));
            
            AppCommunicationUtil.markSuccess(commDto, result);
            return result;
            
        } catch (Exception e) {
            AppCommunicationUtil.markFailure(commDto, 500, e.getMessage());
            throw new ServiceCallException("调用会员服务失败", e);
        } finally {
            saveCommLog(commDto);
        }
    }
    
    /**
     * 会员注册
     */
    public void registerMember(MemberRegisterDTO registerDto) {
        // 实现注册逻辑
        // ...
    }
    
    /**
     * 获取会员订单列表
     */
    public List<OrderDTO> getMemberOrders(Long memberId) {
        // 实现获取订单逻辑
        // ...
    }
    
    private void saveCommLog(AppCommunicationDTO dto) {
        // 同上面的实现
    }
}
```

---

## 📊 DTO 设计规范

### 1. 基础响应 DTO

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class R<T> implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 成功状态码 */
    public static final int SUCCESS = 200;
    
    /** 失败状态码 */
    public static final int FAIL = 500;
    
    private int code;
    private String msg;
    private T data;
    private String traceId;
    private Long timestamp;
    
    public static <T> R<T> ok() {
        return restResult(null, SUCCESS, "操作成功");
    }
    
    public static <T> R<T> ok(T data) {
        return restResult(data, SUCCESS, "操作成功");
    }
    
    public static <T> R<T> ok(String msg) {
        return restResult(null, SUCCESS, msg);
    }
    
    public static <T> R<T> ok(String msg, T data) {
        return restResult(data, SUCCESS, msg);
    }
    
    public static <T> R<T> fail() {
        return restResult(null, FAIL, "操作失败");
    }
    
    public static <T> R<T> fail(String msg) {
        return restResult(null, FAIL, msg);
    }
    
    public static <T> R<T> fail(T data) {
        return restResult(data, FAIL, "操作失败");
    }
    
    public static <T> R<T> fail(String msg, T data) {
        return restResult(data, FAIL, msg);
    }
    
    public static <T> R<T> fail(int code, String msg) {
        return restResult(null, code, msg);
    }
    
    private static <T> R<T> restResult(T data, int code, String msg) {
        return R.<T>builder()
            .code(code)
            .data(data)
            .msg(msg)
            .traceId(MDC.get("traceId"))
            .timestamp(System.currentTimeMillis())
            .build();
    }
    
    public boolean isSuccess() {
        return SUCCESS == code;
    }
}
```

### 2. 后管用户 DTO

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdminUserDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 用户ID */
    private Long userId;
    
    /** 用户名 */
    private String userName;
    
    /** 用户昵称 */
    private String nickName;
    
    /** 邮箱 (脱敏) */
    private String email;
    
    /** 手机号 (脱敏) */
    private String phonenumber;
    
    /** 用户状态 */
    private String status;
    
    /** 创建时间 */
    private LocalDateTime createTime;
    
    /** 角色列表 */
    private List<String> roles;
    
    /** 权限列表 */
    private List<String> permissions;
}
```

### 3. 会员 DTO

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** 会员ID */
    private Long memberId;
    
    /** 会员名称 */
    private String memberName;
    
    /** 手机号 (脱敏) */
    private String phone;
    
    /** 会员等级 */
    private String memberLevel;
    
    /** 会员状态 */
    private String status;
    
    /** 注册时间 */
    private LocalDateTime registerTime;
    
    /** 最后登录时间 */
    private LocalDateTime lastLoginTime;
    
    /** 会员标签 */
    private List<String> tags;
}
```

---

## 🎮 API 控制器实现

### 1. 后管 API 控制器

```java
@RestController
@RequestMapping("/admin/api")
@Slf4j
@Validated
public class AdminUserApiController {
    
    @Autowired
    private ISysUserService userService;
    
    @Autowired
    private ISysRoleService roleService;
    
    /**
     * 根据用户ID获取用户信息
     */
    @GetMapping("/user/{userId}")
    @ServiceAuth("oto-front")  // 自定义注解，限制调用方
    public R<AdminUserDTO> getUserById(@PathVariable @NotNull Long userId) {
        try {
            SysUser user = userService.selectUserById(userId);
            if (user == null) {
                return R.fail("用户不存在");
            }
            
            // 获取用户角色和权限
            List<SysRole> roles = roleService.selectRolesByUserId(userId);
            List<String> roleNames = roles.stream()
                .map(SysRole::getRoleName)
                .collect(Collectors.toList());
            
            List<String> permissions = userService.selectMenuPermsByUserId(userId);
            
            // 构建DTO，注意数据脱敏
            AdminUserDTO dto = AdminUserDTO.builder()
                .userId(user.getUserId())
                .userName(user.getUserName())
                .nickName(user.getNickName())
                .email(maskEmail(user.getEmail()))
                .phonenumber(maskPhone(user.getPhonenumber()))
                .status(user.getStatus())
                .createTime(user.getCreateTime())
                .roles(roleNames)
                .permissions(permissions)
                .build();
            
            return R.ok(dto);
            
        } catch (Exception e) {
            log.error("获取用户信息失败, userId: {}", userId, e);
            return R.fail("获取用户信息失败");
        }
    }
    
    /**
     * 批量获取用户信息
     */
    @PostMapping("/user/batch")
    @ServiceAuth("oto-front")
    public R<List<AdminUserDTO>> getUsersByIds(@RequestBody @NotEmpty List<Long> userIds) {
        try {
            // 限制批量查询数量
            if (userIds.size() > 100) {
                return R.fail("批量查询数量不能超过100");
            }
            
            List<SysUser> users = userService.selectUserByIds(userIds);
            List<AdminUserDTO> dtoList = users.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
            
            return R.ok(dtoList);
            
        } catch (Exception e) {
            log.error("批量获取用户信息失败, userIds: {}", userIds, e);
            return R.fail("批量获取用户信息失败");
        }
    }
    
    /**
     * 获取系统配置
     */
    @GetMapping("/config/{configKey}")
    @ServiceAuth("oto-front")
    public R<String> getConfigValue(@PathVariable @NotBlank String configKey) {
        try {
            // 只允许查询特定的配置项
            if (!isAllowedConfigKey(configKey)) {
                return R.fail("不允许查询该配置项");
            }
            
            String configValue = configService.selectConfigByKey(configKey);
            return R.ok(configValue);
            
        } catch (Exception e) {
            log.error("获取配置失败, configKey: {}", configKey, e);
            return R.fail("获取配置失败");
        }
    }
    
    private AdminUserDTO convertToDTO(SysUser user) {
        return AdminUserDTO.builder()
            .userId(user.getUserId())
            .userName(user.getUserName())
            .nickName(user.getNickName())
            .email(maskEmail(user.getEmail()))
            .phonenumber(maskPhone(user.getPhonenumber()))
            .status(user.getStatus())
            .createTime(user.getCreateTime())
            .build();
    }
    
    private String maskEmail(String email) {
        if (StrUtil.isBlank(email)) {
            return "";
        }
        int atIndex = email.indexOf("@");
        if (atIndex <= 1) {
            return email;
        }
        return email.substring(0, 1) + "***" + email.substring(atIndex);
    }
    
    private String maskPhone(String phone) {
        if (StrUtil.isBlank(phone) || phone.length() < 7) {
            return "";
        }
        return phone.substring(0, 3) + "****" + phone.substring(phone.length() - 4);
    }
    
    private boolean isAllowedConfigKey(String configKey) {
        // 定义允许查询的配置项白名单
        List<String> allowedKeys = Arrays.asList(
            "sys.user.initPassword",
            "sys.index.skinName",
            "sys.user.captchaEnabled"
        );
        return allowedKeys.contains(configKey);
    }
}
```

### 2. 会员 API 控制器

```java
@RestController
@RequestMapping("/app/api")
@Slf4j
@Validated
public class MemberApiController {
    
    @Autowired
    private IOtoMemberService memberService;
    
    /**
     * 根据会员ID获取会员信息
     */
    @GetMapping("/member/{memberId}")
    @ServiceAuth("oto-admin")
    public R<MemberDTO> getMemberById(@PathVariable @NotNull Long memberId) {
        try {
            OtoMember member = memberService.selectByMemberId(memberId);
            if (member == null) {
                return R.fail("会员不存在");
            }
            
            MemberDTO dto = MemberDTO.builder()
                .memberId(member.getMemberId())
                .memberName(member.getMemberName())
                .phone(maskPhone(member.getPhone()))
                .memberLevel(member.getMemberLevel())
                .status(member.getStatus())
                .registerTime(member.getCreateTime())
                .lastLoginTime(member.getLastLoginTime())
                .build();
            
            return R.ok(dto);
            
        } catch (Exception e) {
            log.error("获取会员信息失败, memberId: {}", memberId, e);
            return R.fail("获取会员信息失败");
        }
    }
    
    /**
     * 会员注册
     */
    @PostMapping("/member/register")
    @ServiceAuth("oto-admin")
    public R<Void> registerMember(@RequestBody @Valid MemberRegisterDTO registerDto) {
        try {
            memberService.registerMember(registerDto);
            return R.ok("注册成功");
            
        } catch (Exception e) {
            log.error("会员注册失败", e);
            return R.fail("注册失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取会员订单列表
     */
    @GetMapping("/member/{memberId}/orders")
    @ServiceAuth("oto-admin")
    public R<List<OrderDTO>> getMemberOrders(@PathVariable @NotNull Long memberId,
                                            @RequestParam(defaultValue = "1") Integer pageNum,
                                            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            // 分页查询订单
            PageHelper.startPage(pageNum, pageSize);
            List<OtoOrder> orders = orderService.selectOrdersByMemberId(memberId);
            
            List<OrderDTO> orderDTOs = orders.stream()
                .map(this::convertToOrderDTO)
                .collect(Collectors.toList());
            
            return R.ok(orderDTOs);
            
        } catch (Exception e) {
            log.error("获取会员订单失败, memberId: {}", memberId, e);
            return R.fail("获取订单失败");
        }
    }
    
    private String maskPhone(String phone) {
        if (StrUtil.isBlank(phone) || phone.length() < 7) {
            return "";
        }
        return phone.substring(0, 3) + "****" + phone.substring(phone.length() - 4);
    }
    
    private OrderDTO convertToOrderDTO(OtoOrder order) {
        return OrderDTO.builder()
            .orderId(order.getOrderId())
            .orderNo(order.getOrderNo())
            .orderStatus(order.getOrderStatus())
            .totalAmount(order.getTotalAmount())
            .createTime(order.getCreateTime())
            .build();
    }
}
```

---

## 🔍 监控和日志

### 1. 通信日志记录

利用我们之前创建的 `oto-front-common-mapper` 模块：

```java
@Component
@Slf4j
public class CommunicationLogService {
    
    @Autowired
    private AppCommunicationLogMapper communicationLogMapper;
    
    @Async
    public void saveLog(AppCommunicationDTO dto) {
        try {
            AppCommunicationLog logEntity = AppCommunicationUtil.convertToEntity(dto);
            communicationLogMapper.insert(logEntity);
        } catch (Exception e) {
            log.error("保存通信日志失败", e);
        }
    }
    
    public List<AppCommunicationLogVo> getRecentLogs(String sourceApp, String targetApp, int limit) {
        return communicationLogMapper.selectByApps(sourceApp, targetApp)
            .stream()
            .limit(limit)
            .map(this::convertToVo)
            .collect(Collectors.toList());
    }
    
    public CommunicationStatistics getStatistics(String sourceApp, String targetApp, 
                                                LocalDateTime startTime, LocalDateTime endTime) {
        Long totalCount = communicationLogMapper.countCommunications(sourceApp, targetApp, startTime, endTime);
        Double avgResponseTime = communicationLogMapper.selectAvgResponseTime(sourceApp, targetApp, startTime, endTime);
        
        return CommunicationStatistics.builder()
            .totalCount(totalCount)
            .avgResponseTime(avgResponseTime)
            .build();
    }
}
```

### 2. 性能监控

```java
@Aspect
@Component
@Slf4j
public class ApiCallMonitorAspect {
    
    @Autowired
    private CommunicationLogService logService;
    
    @Around("@annotation(ServiceAuth)")
    public Object monitor(ProceedingJoinPoint point) throws Throwable {
        String methodName = point.getSignature().getName();
        String className = point.getTarget().getClass().getSimpleName();
        
        AppCommunicationDTO dto = AppCommunicationUtil.createCommunicationDTO(
            getCallerApp(), getCurrentApp(), "API_CALL"
        );
        dto.setMethod(methodName);
        dto.setPath(className + "." + methodName);
        
        AppCommunicationUtil.markStart(dto);
        
        try {
            Object result = point.proceed();
            AppCommunicationUtil.markSuccess(dto, "success");
            return result;
            
        } catch (Exception e) {
            AppCommunicationUtil.markFailure(dto, 500, e.getMessage());
            throw e;
            
        } finally {
            logService.saveLog(dto);
        }
    }
    
    private String getCallerApp() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        return request.getHeader("X-Service-Name");
    }
    
    private String getCurrentApp() {
        return "oto-admin"; // 或根据配置动态获取
    }
}
```

### 3. 健康检查

```java
@RestController
@RequestMapping("/admin/api/health")
public class HealthCheckController {
    
    @Autowired
    private MemberServiceClient memberServiceClient;
    
    @GetMapping("/check")
    public R<Map<String, Object>> healthCheck() {
        Map<String, Object> result = new HashMap<>();
        
        // 检查会员服务连通性
        try {
            memberServiceClient.getMemberById(1L);
            result.put("memberService", "UP");
        } catch (Exception e) {
            result.put("memberService", "DOWN");
            result.put("memberServiceError", e.getMessage());
        }
        
        // 检查数据库连通性
        try {
            // 执行简单查询
            result.put("database", "UP");
        } catch (Exception e) {
            result.put("database", "DOWN");
        }
        
        result.put("timestamp", LocalDateTime.now());
        return R.ok(result);
    }
}
```

---

## 📅 实施计划

### 阶段一：基础设施搭建 (第1周)

**Day 1-2: 模块创建和依赖配置**
- [ ] 创建 `oto-common-http-client` 模块
- [ ] 配置 Maven 依赖
- [ ] 创建基础配置类

**Day 3-4: HTTP 客户端实现**
- [ ] 实现 WebClient 配置
- [ ] 创建认证拦截器
- [ ] 实现日志拦截器

**Day 5: 安全认证机制**
- [ ] 实现 API Key 认证
- [ ] 创建服务端验证过滤器
- [ ] 配置安全相关参数

### 阶段二：API 接口开发 (第2周)

**Day 1-2: DTO 设计**
- [ ] 设计统一响应格式
- [ ] 创建后管用户 DTO
- [ ] 创建会员相关 DTO

**Day 3-4: 后管 API 控制器**
- [ ] 实现用户查询接口
- [ ] 实现配置查询接口
- [ ] 添加数据脱敏逻辑

**Day 5: 会员 API 控制器**
- [ ] 实现会员查询接口
- [ ] 实现订单查询接口
- [ ] 添加权限验证

### 阶段三：服务客户端实现 (第3周)

**Day 1-2: 后管服务客户端**
- [ ] 实现 AdminServiceClient
- [ ] 添加重试和超时机制
- [ ] 集成通信日志记录

**Day 3-4: 会员服务客户端**
- [ ] 实现 MemberServiceClient
- [ ] 添加错误处理机制
- [ ] 完善异常处理

**Day 5: 监控和健康检查**
- [ ] 实现性能监控切面
- [ ] 添加健康检查接口
- [ ] 完善日志记录

### 阶段四：测试和优化 (第4周)

**Day 1-2: 单元测试**
- [ ] 编写客户端单元测试
- [ ] 编写控制器单元测试
- [ ] Mock 外部依赖

**Day 3-4: 集成测试**
- [ ] 端到端测试
- [ ] 性能测试
- [ ] 安全测试

**Day 5: 部署和文档**
- [ ] 生产环境配置
- [ ] 部署脚本
- [ ] 使用文档

---

## 🚀 部署配置

### 1. 生产环境配置

**application-prod.yml**:
```yaml
oto:
  services:
    admin:
      base-url: https://admin.yourcompany.com
      timeout: 10000
      retry-count: 3
    member:
      base-url: https://member.yourcompany.com
      timeout: 10000
      retry-count: 3

security:
  service:
    api-key: "${OTO_SERVICE_API_KEY}"
    enabled: true

logging:
  level:
    com.oto.common.http: INFO
    org.springframework.web.reactive.function.client: DEBUG
```

### 2. Docker 配置

**Dockerfile**:
```dockerfile
FROM openjdk:17-jdk-slim

COPY target/oto-admin.jar app.jar

ENV OTO_SERVICE_API_KEY=your-production-api-key
ENV ADMIN_SERVICE_URL=https://admin.yourcompany.com
ENV MEMBER_SERVICE_URL=https://member.yourcompany.com

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 3. Nginx 配置

```nginx
upstream oto-admin {
    server 127.0.0.1:8080;
}

upstream oto-member {
    server 127.0.0.1:8081;
}

server {
    listen 443 ssl;
    server_name admin.yourcompany.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location /admin/api/ {
        proxy_pass http://oto-admin;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # API 限流
        limit_req zone=api burst=10 nodelay;
    }
}
```

---

## 📈 性能优化建议

### 1. 连接池优化

```java
@Bean
public WebClient webClient() {
    ConnectionProvider provider = ConnectionProvider.builder("custom")
        .maxConnections(100)
        .maxIdleTime(Duration.ofSeconds(20))
        .maxLifeTime(Duration.ofSeconds(60))
        .pendingAcquireTimeout(Duration.ofSeconds(60))
        .evictInBackground(Duration.ofSeconds(120))
        .build();
    
    HttpClient httpClient = HttpClient.create(provider)
        .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 5000)
        .responseTimeout(Duration.ofSeconds(10))
        .doOnConnected(conn -> 
            conn.addHandlerLast(new ReadTimeoutHandler(10))
                .addHandlerLast(new WriteTimeoutHandler(10)));
    
    return WebClient.builder()
        .clientConnector(new ReactorClientHttpConnector(httpClient))
        .build();
}
```

### 2. 缓存策略

```java
@Service
public class CachedAdminServiceClient {
    
    @Autowired
    private AdminServiceClient adminServiceClient;
    
    @Cacheable(value = "adminUser", key = "#userId", unless = "#result == null")
    public AdminUserDTO getUserById(Long userId) {
        return adminServiceClient.getUserById(userId);
    }
    
    @CacheEvict(value = "adminUser", key = "#userId")
    public void evictUserCache(Long userId) {
        // 缓存失效
    }
}
```

### 3. 异步调用

```java
@Service
public class AsyncServiceClient {
    
    @Async
    public CompletableFuture<AdminUserDTO> getUserByIdAsync(Long userId) {
        AdminUserDTO user = adminServiceClient.getUserById(userId);
        return CompletableFuture.completedFuture(user);
    }
    
    public void batchProcessUsers(List<Long> userIds) {
        List<CompletableFuture<AdminUserDTO>> futures = userIds.stream()
            .map(this::getUserByIdAsync)
            .collect(Collectors.toList());
        
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
            .thenApply(v -> futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList()))
            .thenAccept(this::processUsers);
    }
}
```

---

## 🔧 故障排查指南

### 1. 常见问题

**连接超时**:
```
原因: 网络延迟或目标服务响应慢
解决: 调整超时配置，检查网络状况
```

**认证失败**:
```
原因: API Key 不匹配或过期
解决: 检查配置文件中的 API Key
```

**序列化错误**:
```
原因: DTO 字段不匹配或类型转换失败
解决: 检查 DTO 定义，确保字段一致
```

### 2. 监控指标

```java
@Component
public class ServiceMetrics {
    
    private final MeterRegistry meterRegistry;
    private final Counter successCounter;
    private final Counter failureCounter;
    private final Timer responseTimer;
    
    public ServiceMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.successCounter = Counter.builder("service.call.success")
            .description("Successful service calls")
            .register(meterRegistry);
        this.failureCounter = Counter.builder("service.call.failure")
            .description("Failed service calls")
            .register(meterRegistry);
        this.responseTimer = Timer.builder("service.call.duration")
            .description("Service call duration")
            .register(meterRegistry);
    }
    
    public void recordSuccess() {
        successCounter.increment();
    }
    
    public void recordFailure() {
        failureCounter.increment();
    }
    
    public Timer.Sample startTimer() {
        return Timer.start(meterRegistry);
    }
}
```

---

## 📝 总结

本改造方案通过引入 REST HTTP 通信机制，彻底解决了两个应用间直接数据库通信的安全隐患，同时提供了完善的监控、日志和治理能力。

### 主要收益

1. **安全性提升**: API Key 认证 + 数据脱敏
2. **可维护性**: 标准化接口 + 统一错误处理
3. **可观测性**: 完整的调用链路追踪
4. **可扩展性**: 为未来微服务化奠定基础

### 风险控制

1. **渐进式改造**: 保留数据库访问作为降级方案
2. **完善监控**: 及时发现和处理问题
3. **充分测试**: 确保改造后功能正常

通过本方案的实施，您的系统将具备更好的安全性、可维护性和扩展性，为未来的业务发展奠定坚实基础。
