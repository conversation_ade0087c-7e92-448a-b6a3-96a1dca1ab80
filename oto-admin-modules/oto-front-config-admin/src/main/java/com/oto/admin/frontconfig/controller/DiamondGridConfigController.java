package com.oto.admin.frontconfig.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.oto.admin.frontconfig.service.DiamondGridConfigService;
import com.oto.common.core.domain.R;
import com.oto.common.domain.vo.OtoDiamondGridConfigVo;
import com.oto.common.http.annotation.RemoteServicePort;
import com.oto.common.log.annotation.Log;
import com.oto.common.log.enums.BusinessType;
import com.oto.common.web.core.BaseController;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 金刚位网格配置管理（基于系统配置表）
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/otoconfig/diamond-grid")
public class DiamondGridConfigController extends BaseController {

    private final DiamondGridConfigService  diamondConfigService;

    /**
     * 获取当前网格配置
     */
    @SaCheckPermission("otoconfig:diamond-grid:list")
    @GetMapping("/config")
    public R<OtoDiamondGridConfigVo> getActiveConfig() {
        return R.ok(diamondConfigService.getActiveGridConfig());
    }

    /**
     * 分页查询网格配置列表（兼容前端，实际只返回当前配置）
     */
    @SaCheckPermission("otoconfig:diamond-grid:list")
    @GetMapping("/page")
    public R<OtoDiamondGridConfigVo> page() {
        // 由于迁移到系统配置表，只有一个配置，直接返回当前配置
        return R.ok(diamondConfigService.getActiveGridConfig());
    }

    /**
     * 查询网格配置详细
     */
    @SaCheckPermission("otoconfig:diamond-grid:query")
    @GetMapping("/{id}")
    public R<OtoDiamondGridConfigVo> getInfo(@PathVariable Long id) {
        // 由于只有一个配置，忽略ID参数，直接返回当前配置
        return R.ok(diamondConfigService.getActiveGridConfig());
    }

    /**
     * 新增网格配置（实际是更新配置）
     */
    @SaCheckPermission("otoconfig:diamond-grid:add")
    @Log(title = "金刚位网格配置", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Valid @RequestBody OtoDiamondGridConfigVo config) {
        // 由于只有一个配置，新增操作实际是更新操作
        return toAjax(diamondConfigService.saveGridConfig(config));
    }

    /**
     * 修改网格配置
     */
    @SaCheckPermission("otoconfig:diamond-grid:edit")
    @Log(title = "金刚位网格配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Valid @RequestBody OtoDiamondGridConfigVo config) {
        return toAjax(diamondConfigService.saveGridConfig(config));
    }

    /**
     * 新增行配置
     */
    @SaCheckPermission("otoconfig:diamond-grid:add")
    @Log(title = "新增网格行配置", businessType = BusinessType.INSERT)
    @PostMapping("/row")
    public R<Void> addRow(@Valid @RequestBody OtoDiamondGridConfigVo.RowConfig rowConfig) {
        return toAjax(diamondConfigService.addRow(rowConfig));
    }

    /**
     * 编辑行配置
     */
    @SaCheckPermission("otoconfig:diamond-grid:edit")
    @Log(title = "编辑网格行配置", businessType = BusinessType.UPDATE)
    @PutMapping("/row/{rowId}")
    public R<Void> editRow(@PathVariable Integer rowId, @Valid @RequestBody OtoDiamondGridConfigVo.RowConfig rowConfig) {
        return toAjax(diamondConfigService.editRow(rowId, rowConfig));
    }

    /**
     * 删除指定行配置
     */
    @SaCheckPermission("otoconfig:diamond-grid:remove")
    @Log(title = "删除网格行配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/row/{rowId}")
    public R<Void> removeRow(@PathVariable Integer rowId) {
        return toAjax(diamondConfigService.removeRow(rowId));
    }

    /**
     * 删除网格配置（不支持，返回错误）
     */
    @SaCheckPermission("otoconfig:diamond-grid:remove")
    @Log(title = "金刚位网格配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@PathVariable Long[] ids) {
        return R.fail("网格配置不支持删除操作，请使用更新功能");
    }

    /**
     * 激活指定的网格配置（兼容接口，实际无操作）
     */
    @SaCheckPermission("otoconfig:diamond-grid:edit")
    @Log(title = "激活网格配置", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/activate")
    public R<Void> activateConfig(@PathVariable Long id) {
        // 由于只有一个配置，激活操作无实际意义
        return R.ok();
    }

    /**
     * 获取网格配置（包含行配置）
     */
    @SaCheckPermission("otoconfig:diamond-grid:list")
    @GetMapping("/{id}/with-rows")
    public R<OtoDiamondGridConfigVo> getConfigWithRows(@PathVariable Long id) {
        // 配置已经包含行配置，直接返回当前配置
        return R.ok(diamondConfigService.getActiveGridConfig());
    }
}
