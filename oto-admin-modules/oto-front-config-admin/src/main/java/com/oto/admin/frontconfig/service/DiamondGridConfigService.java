package com.oto.admin.frontconfig.service;

import com.oto.admin.remote.feign.DiamondGridConfigFeignClient;
import com.oto.common.core.domain.R;
import com.oto.common.domain.vo.OtoDiamondGridConfigVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 网格配置管理服务
 * 与 DiamondGridConfigFeignClient 完全对应
 * 每个方法签名与 FeignClient 完全一样，只负责调用对应的 FeignClient 方法
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DiamondGridConfigService {

    private final DiamondGridConfigFeignClient diamondGridConfigFeignClient;

    /**
     * 获取当前激活的网格配置
     * 对应 FeignClient 方法: getActiveConfig()
     */
    public OtoDiamondGridConfigVo getActiveGridConfig() {
        return diamondGridConfigFeignClient.getActiveConfig().getData();
    }

    /**
     * 分页查询网格配置列表（兼容前端，实际只返回当前配置）
     * 对应 FeignClient 方法: page()
     */
    public R<OtoDiamondGridConfigVo> page() {
        return diamondGridConfigFeignClient.page();
    }

    /**
     * 根据ID获取网格配置详细信息
     * 对应 FeignClient 方法: getInfo(Long id)
     */
    public R<OtoDiamondGridConfigVo> getInfo(Long id) {
        return diamondGridConfigFeignClient.getInfo(id);
    }

    /**
     * 保存网格配置（新增或修改）
     * 对应 FeignClient 方法: add(OtoDiamondGridConfigVo config) 和 edit(OtoDiamondGridConfigVo config)
     */
    public Boolean saveGridConfig(OtoDiamondGridConfigVo config) {
        R<Void> result;
        if (config.getId() == null) {
            result = diamondGridConfigFeignClient.add(config);
        } else {
            result = diamondGridConfigFeignClient.edit(config);
        }
        return result.getCode() == 200;
    }

    /**
     * 新增网格行配置
     * 对应 FeignClient 方法: addRow(OtoDiamondGridConfigVo.RowConfig rowConfig)
     */
    public Boolean addRow(OtoDiamondGridConfigVo.RowConfig rowConfig) {
        R<Void> result = diamondGridConfigFeignClient.addRow(rowConfig);
        return result.getCode() == 200;
    }

    /**
     * 编辑网格行配置
     * 对应 FeignClient 方法: editRow(Integer rowId, OtoDiamondGridConfigVo.RowConfig rowConfig)
     */
    public Boolean editRow(Integer rowId, OtoDiamondGridConfigVo.RowConfig rowConfig) {
        R<Void> result = diamondGridConfigFeignClient.editRow(rowId, rowConfig);
        return result.getCode() == 200;
    }

    /**
     * 删除网格行配置
     * 对应 FeignClient 方法: removeRow(Integer rowId)
     */
    public Boolean removeRow(Integer rowId) {
        R<Void> result = diamondGridConfigFeignClient.removeRow(rowId);
        return result.getCode() == 200;
    }

    /**
     * 删除网格配置（不支持，返回错误）
     * 对应 FeignClient 方法: remove(Long[] ids)
     */
    public R<Void> remove(Long[] ids) {
        return diamondGridConfigFeignClient.remove(ids);
    }

    /**
     * 激活指定的网格配置（兼容接口，实际无操作）
     * 对应 FeignClient 方法: activateConfig(Long id)
     */
    public R<Void> activateConfig(Long id) {
        return diamondGridConfigFeignClient.activateConfig(id);
    }

    /**
     * 获取网格配置（包含行配置）
     * 对应 FeignClient 方法: getConfigWithRows(Long id)
     */
    public R<OtoDiamondGridConfigVo> getConfigWithRows(Long id) {
        return diamondGridConfigFeignClient.getConfigWithRows(id);
    }



}
