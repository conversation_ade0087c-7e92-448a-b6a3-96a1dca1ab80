package com.oto.admin.frontconfig.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.oto.common.idempotent.annotation.RepeatSubmit;
import com.oto.common.log.annotation.Log;
import com.oto.common.web.core.BaseController;
import com.oto.common.mybatis.core.page.PageQuery;
import com.oto.common.core.domain.R;
import com.oto.common.core.validate.AddGroup;
import com.oto.common.core.validate.EditGroup;
import com.oto.common.log.enums.BusinessType;
import com.oto.common.excel.utils.ExcelUtil;
import com.oto.common.domain.vo.OtoConfigVo;
import com.oto.common.domain.bo.OtoConfigBo;
import com.oto.admin.frontconfig.service.OtoConfigService;
import com.oto.common.mybatis.core.page.TableDataInfo;
import com.oto.common.core.utils.StringUtils;

/**
 * oto参数配置管理控制器
 * 通过远程调用 oto-front-system-config 模块的基础能力
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/otoconfig/otoconfig")
public class OtoConfigController extends BaseController {

    private final OtoConfigService otoConfigService;

    /**
     * 查询oto参数配置列表
     */
    @SaCheckPermission("otoconfig:otoconfig:list")
    @GetMapping("/list")
    public TableDataInfo<OtoConfigVo> list(OtoConfigBo bo, PageQuery pageQuery) {
        return otoConfigService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出oto参数配置列表
     */
    @SaCheckPermission("otoconfig:otoconfig:export")
    @Log(title = "oto参数配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OtoConfigBo bo, HttpServletResponse response) {
        List<OtoConfigVo> list = otoConfigService.queryList(bo);
        ExcelUtil.exportExcel(list, "oto参数配置", OtoConfigVo.class, response);
    }

    /**
     * 获取oto参数配置详细信息
     *
     * @param configId 主键
     */
    @SaCheckPermission("otoconfig:otoconfig:query")
    @GetMapping("/{configId}")
    public R<OtoConfigVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long configId) {
        return R.ok(otoConfigService.queryById(configId));
    }

    /**
     * 新增oto参数配置
     */
    @SaCheckPermission("otoconfig:otoconfig:add")
    @Log(title = "oto参数配置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OtoConfigBo bo) {
        return toAjax(StringUtils.isNotEmpty(otoConfigService.insertByBo(bo)));
    }

    /**
     * 修改oto参数配置
     */
    @SaCheckPermission("otoconfig:otoconfig:edit")
    @Log(title = "oto参数配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OtoConfigBo bo) {
        return toAjax(StringUtils.isNotEmpty(otoConfigService.updateByBo(bo)));
    }

    /**
     * 删除oto参数配置
     *
     * @param configIds 主键串
     */
    @SaCheckPermission("otoconfig:otoconfig:remove")
    @Log(title = "oto参数配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{configIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] configIds) {
        return toAjax(otoConfigService.deleteWithValidByIds(List.of(configIds), true));
    }

    /**
     * 刷新所有配置缓存
     */
    @SaCheckPermission("otoconfig:otoconfig:refresh")
    @Log(title = "oto参数配置", businessType = BusinessType.OTHER)
    @DeleteMapping("/refresh")
    public R<Void> refreshCache() {
        boolean success = otoConfigService.refreshOtoAllConfigCache();
        return success ? R.ok() : R.fail("刷新缓存失败");
    }

    /**
     * 根据配置键获取配置值
     *
     * @param configKey 配置键
     */
    @SaCheckPermission("otoconfig:otoconfig:query")
    @GetMapping("/value/{configKey}")
    public R<String> getConfigValueByKey(@PathVariable String configKey) {
        String value = otoConfigService.getConfigValueByKey(configKey);
        return R.ok(value);
    }

    /**
     * 根据配置键获取开关配置值
     *
     * @param configKey 配置键
     */
    @SaCheckPermission("otoconfig:otoconfig:query")
    @GetMapping("/enable/{configKey}")
    public R<Boolean> getEnableByConfigKey(@PathVariable String configKey) {
        Boolean enable = otoConfigService.getEnableByConfigKey(configKey);
        return R.ok(enable);
    }
}
