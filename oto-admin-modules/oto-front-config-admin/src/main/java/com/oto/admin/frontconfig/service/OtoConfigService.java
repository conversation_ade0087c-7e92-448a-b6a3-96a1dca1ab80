package com.oto.admin.frontconfig.service;

import com.oto.admin.remote.feign.OtoConfigFeignClient;
import com.oto.common.core.domain.R;
import com.oto.common.domain.bo.OtoConfigBo;
import com.oto.common.domain.vo.OtoConfigVo;
import com.oto.common.mybatis.core.page.PageQuery;
import com.oto.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * oto参数配置管理服务
 * 与 OtoConfigFeignClient 完全对应
 * 每个方法签名与 FeignClient 完全一样，只负责调用对应的 FeignClient 方法
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OtoConfigService {

    private final OtoConfigFeignClient otoConfigFeignClient;

    /**
     * 查询oto参数配置列表
     * 对应 FeignClient 方法: list()
     */
    public TableDataInfo<OtoConfigVo> queryPageList(OtoConfigBo bo, PageQuery pageQuery) {
        log.info("📡 远程调用查询oto参数配置列表: pageNum={}, pageSize={}",
                pageQuery.getPageNum(), pageQuery.getPageSize());
        try {
            TableDataInfo<OtoConfigVo> result = otoConfigFeignClient.list(bo, pageQuery);
            log.info("✅ 远程调用成功，返回{}条记录", result.getTotal());
            return result;
        } catch (Exception e) {
            log.error("❌ 远程调用查询oto参数配置列表失败", e);
            throw new RuntimeException("查询配置列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 导出oto参数配置列表
     * 对应 FeignClient 方法: export()
     */
    public List<OtoConfigVo> queryList(OtoConfigBo bo) {
        log.info("📡 远程调用导出oto参数配置列表");
        try {
            R<List<OtoConfigVo>> result = otoConfigFeignClient.export(bo);
            if (result.isSuccess()) {
                log.info("✅ 远程调用成功，返回{}条记录", result.getData().size());
                return result.getData();
            } else {
                log.error("❌ 远程调用失败: {}", result.getMsg());
                throw new RuntimeException("导出配置列表失败: " + result.getMsg());
            }
        } catch (Exception e) {
            log.error("❌ 远程调用导出oto参数配置列表失败", e);
            throw new RuntimeException("导出配置列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取oto参数配置详细信息
     * 对应 FeignClient 方法: getInfo()
     */
    public OtoConfigVo queryById(Long configId) {
        log.info("📡 远程调用查询oto参数配置详情: configId={}", configId);
        try {
            R<OtoConfigVo> result = otoConfigFeignClient.getInfo(configId);
            if (result.isSuccess(result)) {
                log.info("✅ 远程调用成功，获取配置详情");
                return result.getData();
            } else {
                log.error("❌ 远程调用失败: {}", result.getMsg());
                throw new RuntimeException("查询配置详情失败: " + result.getMsg());
            }
        } catch (Exception e) {
            log.error("❌ 远程调用查询oto参数配置详情失败", e);
            throw new RuntimeException("查询配置详情失败: " + e.getMessage(), e);
        }
    }

    /**
     * 新增oto参数配置
     * 对应 FeignClient 方法: add()
     */
    public String insertByBo(OtoConfigBo bo) {
        log.info("📡 远程调用新增oto参数配置: configKey={}", bo.getConfigKey());
        try {
            R<String> result = otoConfigFeignClient.add(bo);
            if (result.isSuccess()) {
                log.info("✅ 远程调用成功，新增配置完成");
                return result.getData();
            } else {
                log.error("❌ 远程调用失败: {}", result.getMsg());
                throw new RuntimeException("新增配置失败: " + result.getMsg());
            }
        } catch (Exception e) {
            log.error("❌ 远程调用新增oto参数配置失败", e);
            throw new RuntimeException("新增配置失败: " + e.getMessage(), e);
        }
    }

    /**
     * 修改oto参数配置
     * 对应 FeignClient 方法: edit()
     */
    public String updateByBo(OtoConfigBo bo) {
        log.info("📡 远程调用修改oto参数配置: configId={}, configKey={}",
                bo.getConfigId(), bo.getConfigKey());
        try {
            R<String> result = otoConfigFeignClient.edit(bo);
            if (result.isSuccess()) {
                log.info("✅ 远程调用成功，修改配置完成");
                return result.getData();
            } else {
                log.error("❌ 远程调用失败: {}", result.getMsg());
                throw new RuntimeException("修改配置失败: " + result.getMsg());
            }
        } catch (Exception e) {
            log.error("❌ 远程调用修改oto参数配置失败", e);
            throw new RuntimeException("修改配置失败: " + e.getMessage(), e);
        }
    }

    /**
     * 删除oto参数配置
     * 对应 FeignClient 方法: remove()
     */
    public Boolean deleteWithValidByIds(List<Long> configIds, boolean isValid) {
        log.info("📡 远程调用删除oto参数配置: configIds={}", configIds);
        try {
            Long[] ids = configIds.toArray(new Long[0]);
            R<Void> result = otoConfigFeignClient.remove(ids);
            if (result.isSuccess()) {
                log.info("✅ 远程调用成功，删除配置完成");
                return true;
            } else {
                log.error("❌ 远程调用失败: {}", result.getMsg());
                return false;
            }
        } catch (Exception e) {
            log.error("❌ 远程调用删除oto参数配置失败", e);
            return false;
        }
    }

    /**
     * 刷新所有配置缓存
     * 对应 FeignClient 方法: refreshCache()
     */
    public boolean refreshOtoAllConfigCache() {
        log.info("📡 远程调用刷新所有配置缓存");
        try {
            R<Void> result = otoConfigFeignClient.refreshCache();
            if (result.isSuccess()) {
                log.info("✅ 远程调用成功，刷新缓存完成");
                return true;
            } else {
                log.error("❌ 远程调用失败: {}", result.getMsg());
                return false;
            }
        } catch (Exception e) {
            log.error("❌ 远程调用刷新配置缓存失败", e);
            return false;
        }
    }

    /**
     * 根据配置键获取配置值
     * 对应 FeignClient 方法: getConfigValueByKey()
     */
    public String getConfigValueByKey(String configKey) {
        log.info("📡 远程调用获取配置值: configKey={}", configKey);
        try {
            R<String> result = otoConfigFeignClient.getConfigValueByKey(configKey);
            if (result.isSuccess()) {
                log.info("✅ 远程调用成功，获取配置值: {}", result.getData());
                return result.getData();
            } else {
                log.error("❌ 远程调用失败: {}", result.getMsg());
                return null;
            }
        } catch (Exception e) {
            log.error("❌ 远程调用获取配置值失败", e);
            return null;
        }
    }

    /**
     * 根据配置键获取开关配置值
     * 对应 FeignClient 方法: getEnableByConfigKey()
     */
    public Boolean getEnableByConfigKey(String configKey) {
        log.info("📡 远程调用获取开关配置值: configKey={}", configKey);
        try {
            R<Boolean> result = otoConfigFeignClient.getEnableByConfigKey(configKey);
            if (result.isSuccess()) {
                log.info("✅ 远程调用成功，获取开关值: {}", result.getData());
                return result.getData();
            } else {
                log.error("❌ 远程调用失败: {}", result.getMsg());
                return false;
            }
        } catch (Exception e) {
            log.error("❌ 远程调用获取开关配置值失败", e);
            return false;
        }
    }
}
