package com.oto.admin.frontconfig.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.oto.admin.frontconfig.service.DiamondLayoutService;
import com.oto.common.core.domain.R;
import com.oto.common.domain.model.GridLayoutData;
import com.oto.common.domain.model.PositionUpdateItem;
import com.oto.common.domain.request.AutoAssignRequest;
import com.oto.common.domain.request.PositionUpdateRequest;
import com.oto.common.domain.vo.OtoDiamondPositionVo;
import com.oto.common.http.annotation.RemoteServicePort;
import com.oto.common.log.annotation.Log;
import com.oto.common.log.enums.BusinessType;
import com.oto.common.web.core.BaseController;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 金刚位布局管理控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/otoconfig/diamond-layout")
@RequiredArgsConstructor
@Validated
public class DiamondLayoutController extends BaseController {

    private final DiamondLayoutService diamondConfigService;

    /**
     * 获取完整的网格布局数据
     */
    @SaCheckPermission("otoconfig:diamond-grid:list")
    @GetMapping("/complete")
    public R<GridLayoutData> getCompleteLayout() {
        return R.ok(diamondConfigService.getCompleteLayout());
    }

    /**
     * 获取未使用的金刚位列表
     */
    @SaCheckPermission("otoconfig:diamond-position:list")
    @GetMapping("/unused")
    public R<List<OtoDiamondPositionVo>> getUnusedPositions() {
        return R.ok(diamondConfigService.getUnusedPositions());
    }

    /**
     * 批量更新金刚位位置
     */
    @SaCheckPermission("otoconfig:diamond-position:update-positions")
    @Log(title = "金刚位位置更新", businessType = BusinessType.UPDATE)
    @PutMapping("/positions")
    public R<Void> updatePositions(@RequestBody PositionUpdateRequest request) {
        return toAjax(diamondConfigService.updatePositions(request));
    }



    /**
     * 自动分配位置
     */
    @SaCheckPermission("otoconfig:diamond-position:auto-assign")
    @Log(title = "金刚位自动分配", businessType = BusinessType.UPDATE)
    @PostMapping("/auto-assign")
    public R<Void> autoAssignPositions(@RequestBody AutoAssignRequest request) {
        return toAjax(diamondConfigService.autoAssignPositions(request));
    }






}
