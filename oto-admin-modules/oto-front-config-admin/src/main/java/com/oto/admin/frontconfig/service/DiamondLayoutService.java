package com.oto.admin.frontconfig.service;

import com.oto.admin.remote.feign.DiamondLayoutFeignClient;
import com.oto.common.core.domain.R;
import com.oto.common.domain.model.GridLayoutData;
import com.oto.common.domain.model.PositionUpdateItem;
import com.oto.common.domain.request.AutoAssignRequest;
import com.oto.common.domain.request.PositionUpdateRequest;
import com.oto.common.domain.vo.OtoDiamondPositionVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 金刚位布局管理服务
 * 通过 FeignClient 调用远程服务
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DiamondLayoutService {

    private final DiamondLayoutFeignClient diamondLayoutFeignClient;

    /**
     * 获取完整的网格布局数据
     * 对应 FeignClient 方法: getCompleteLayout()
     */
    public GridLayoutData getCompleteLayout() {
        return diamondLayoutFeignClient.getCompleteLayout().getData();
    }

    /**
     * 获取未使用的金刚位列表
     * 对应 FeignClient 方法: getUnusedPositions()
     */
    public List<OtoDiamondPositionVo> getUnusedPositions() {
        return diamondLayoutFeignClient.getUnusedPositions().getData();
    }

    /**
     * 批量更新金刚位位置
     * 对应 FeignClient 方法: updatePositions(PositionUpdateRequest request)
     */
    public Boolean updatePositions(PositionUpdateRequest request) {
        R<Void> result = diamondLayoutFeignClient.updatePositions(request);
        return result.getCode() == 200;
    }

    /**
     * 自动分配位置
     * 对应 FeignClient 方法: autoAssignPositions(AutoAssignRequest request)
     */
    public Boolean autoAssignPositions(AutoAssignRequest request) {
        R<Void> result = diamondLayoutFeignClient.autoAssignPositions(request);
        return result.getCode() == 200;
    }


}
