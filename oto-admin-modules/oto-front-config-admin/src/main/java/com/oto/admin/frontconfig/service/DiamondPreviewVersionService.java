package com.oto.admin.frontconfig.service;

import com.oto.admin.remote.feign.DiamondPreviewVersionFeignClient;
import com.oto.common.core.domain.R;
import com.oto.common.domain.bo.OtoDiamondPreviewVersionBo;
import com.oto.common.domain.request.CreateVersionRequest;
import com.oto.common.domain.vo.OtoDiamondPreviewVersionVo;
import com.oto.common.mybatis.core.page.PageQuery;
import com.oto.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 预览版本管理服务
 * 通过 FeignClient 调用远程服务
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DiamondPreviewVersionService {

    private final DiamondPreviewVersionFeignClient diamondPreviewVersionFeignClient;

    /**
     * 分页查询预览版本列表
     */
    public R<TableDataInfo<OtoDiamondPreviewVersionVo>> page(OtoDiamondPreviewVersionBo bo, PageQuery pageQuery){
        return diamondPreviewVersionFeignClient.page(bo, pageQuery);
    }

    /**
     * 获取当前发布版本
     */
    public OtoDiamondPreviewVersionVo getCurrentVersion() {
        return diamondPreviewVersionFeignClient.getCurrentVersion().getData();
    }

    /**
     * 根据版本编码获取预览版本详情
     */
    public OtoDiamondPreviewVersionVo getByVersionCode(String versionCode) {
        return diamondPreviewVersionFeignClient.getByVersionCode(versionCode).getData();
    }

    /**
     * 根据ID获取预览版本详情
     */
    public OtoDiamondPreviewVersionVo queryById(Long id) {
        return diamondPreviewVersionFeignClient.getInfo(id).getData();
    }

    /**
     * 新增预览版本
     */
    public Boolean insertByBo(OtoDiamondPreviewVersionBo bo) {
        R<Void> result = diamondPreviewVersionFeignClient.add(bo);
        return result.getCode() == 200;
    }

    /**
     * 创建预览版本
     */
    public String createPreviewVersion(CreateVersionRequest request) {
        return diamondPreviewVersionFeignClient.createPreviewVersion(request).getData();
    }

    /**
     * 更新预览版本
     */
    public Boolean updateByBo(OtoDiamondPreviewVersionBo bo) {
        R<Void> result = diamondPreviewVersionFeignClient.edit(bo);
        return result.getCode() == 200;
    }

    /**
     * 更新版本状态
     */
    public Boolean updateStatus(String versionCode, Map<String, Integer> request) {
        R<Void> result = diamondPreviewVersionFeignClient.updateStatus(versionCode, request);
        return result.getCode() == 200;
    }

    /**
     * 发布预览版本
     */
    public Boolean publishVersion(String versionCode) {
        R<Void> result = diamondPreviewVersionFeignClient.publishVersion(versionCode);
        return result.getCode() == 200;
    }

    /**
     * 回滚预览版本
     */
    public Boolean rollbackVersion(String versionCode) {
        R<Void> result = diamondPreviewVersionFeignClient.rollbackVersion(versionCode);
        return result.getCode() == 200;
    }

    /**
     * 生成预览URL
     */
    public String generatePreviewUrl(String versionCode) {
        return diamondPreviewVersionFeignClient.generatePreviewUrl(versionCode).getData();
    }

    /**
     * 删除预览版本
     */
    public Boolean deleteWithValidByIds(Long[] ids) {
        R<Void> result = diamondPreviewVersionFeignClient.remove(ids);
        return result.getCode() == 200;
    }

}
