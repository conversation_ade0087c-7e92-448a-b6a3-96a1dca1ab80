package com.oto.admin.frontconfig.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.oto.admin.frontconfig.service.DiamondPositionService;
import com.oto.common.core.domain.R;
import com.oto.common.core.validate.AddGroup;
import com.oto.common.core.validate.EditGroup;
import com.oto.common.domain.bo.OtoDiamondPositionBo;
import com.oto.common.domain.vo.OtoDiamondPositionVo;
import com.oto.common.excel.utils.ExcelUtil;

import com.oto.common.log.annotation.Log;
import com.oto.common.log.enums.BusinessType;
import com.oto.common.mybatis.core.page.PageQuery;
import com.oto.common.mybatis.core.page.TableDataInfo;
import com.oto.common.web.core.BaseController;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 金刚位配置管理
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/otoconfig/diamond-position")
public class OtoDiamondPositionController extends BaseController {

    private final DiamondPositionService otoDiamondPositionService;

    /**
     * 分页查询金刚位列表
     */
    @SaCheckPermission("otoconfig:diamond-position:list")
    @GetMapping("/page")
    public R<TableDataInfo<OtoDiamondPositionVo>> page(OtoDiamondPositionBo bo, PageQuery pageQuery) {
        return otoDiamondPositionService.page(bo, pageQuery);
    }

    /**
     * 获取所有启用的金刚位（用于前端展示）
     */
    @SaCheckPermission("otoconfig:diamond-position:list")
    @GetMapping("/list")
    public R<List<OtoDiamondPositionVo>> list() {
        return R.ok(otoDiamondPositionService.queryEnabledList());
    }



    /**
     * 根据ID获取金刚位详情
     */
    @SaCheckPermission("otoconfig:diamond-position:list")
    @GetMapping("/{id}")
    public R<OtoDiamondPositionVo> getInfo(@NotNull(message = "主键不能为空")
                                           @PathVariable Long id) {
        return R.ok(otoDiamondPositionService.queryById(id));
    }

    /**
     * 新增金刚位
     */
    @SaCheckPermission("otoconfig:diamond-position:add")
    @Log(title = "金刚位配置", businessType = BusinessType.INSERT)

    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OtoDiamondPositionBo bo) {
        return toAjax(otoDiamondPositionService.insertByBo(bo));
    }

    /**
     * 更新金刚位
     */
    @SaCheckPermission("otoconfig:diamond-position:edit")
    @Log(title = "金刚位配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OtoDiamondPositionBo bo) {
        return toAjax(otoDiamondPositionService.updateByBo(bo));
    }

    /**
     * 删除金刚位
     */
    @SaCheckPermission("otoconfig:diamond-position:remove")
    @Log(title = "金刚位配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public R<Void> remove(@NotNull(message = "主键不能为空")
                          @PathVariable Long id) {
        return toAjax(otoDiamondPositionService.deleteWithValidByIds(List.of(id), true));
    }



    /**
     * 更新金刚位排序
     */
    @SaCheckPermission("otoconfig:diamond-position:sort")
    @Log(title = "金刚位配置", businessType = BusinessType.UPDATE)
    @PutMapping("/sort")
    public R<Void> updateSort(@RequestBody List<OtoDiamondPositionBo> sortList) {
        return toAjax(otoDiamondPositionService.updateSort(sortList));
    }

    /**
     * 启用/禁用金刚位
     */
    @SaCheckPermission("otoconfig:diamond-position:edit")
    @Log(title = "金刚位配置", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/status")
    public R<Void> updateStatus(@PathVariable Long id,
                                @RequestParam Integer status) {
        return toAjax(otoDiamondPositionService.updateStatus(id, status));
    }

    /**
     * 批量更新状态
     */
    @SaCheckPermission("otoconfig:diamond-position:edit")
    @Log(title = "金刚位配置", businessType = BusinessType.UPDATE)
    @PatchMapping("/batch/status")
    public R<Void> batchUpdateStatus(@RequestBody Map<String, Object> request) {
        return toAjax(otoDiamondPositionService.batchUpdateStatus(request));
    }



}
