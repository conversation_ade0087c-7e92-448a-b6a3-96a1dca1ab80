package com.oto.admin.frontconfig.service;

import com.oto.admin.remote.feign.DiamondPositionFeignClient;
import com.oto.common.core.domain.R;
import com.oto.common.domain.bo.OtoDiamondPositionBo;
import com.oto.common.domain.vo.OtoDiamondPositionVo;
import com.oto.common.mybatis.core.page.PageQuery;
import com.oto.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 金刚位配置管理服务
 * 通过 FeignClient 调用远程服务
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DiamondPositionService {

    private final DiamondPositionFeignClient diamondPositionFeignClient;

    /**
     * 分页查询金刚位列表
     * 对应 FeignClient 方法: page(Map<String, Object> params)
     */
    public R<TableDataInfo<OtoDiamondPositionVo>> page(OtoDiamondPositionBo bo, PageQuery pageQuery)  {
        // 将 BO 和 PageQuery 转换为 Map 参数


        return diamondPositionFeignClient.page(bo, pageQuery);
    }

    /**
     * 获取所有启用的金刚位（用于前端展示）
     * 对应 FeignClient 方法: list()
     */
    public List<OtoDiamondPositionVo> queryEnabledList() {
        return diamondPositionFeignClient.list().getData();
    }

    /**
     * 根据ID获取金刚位详情
     * 对应 FeignClient 方法: getInfo(Long id)
     */
    public OtoDiamondPositionVo queryById(Long id) {
        return diamondPositionFeignClient.getInfo(id).getData();
    }

    /**
     * 新增金刚位
     * 对应 FeignClient 方法: add(OtoDiamondPositionBo bo)
     */
    public Boolean insertByBo(OtoDiamondPositionBo bo) {
        R<Void> result = diamondPositionFeignClient.add(bo);
        return result.getCode() == 200;
    }

    /**
     * 更新金刚位
     * 对应 FeignClient 方法: edit(OtoDiamondPositionBo bo)
     */
    public Boolean updateByBo(OtoDiamondPositionBo bo) {
        R<Void> result = diamondPositionFeignClient.edit(bo);
        return result.getCode() == 200;
    }

    /**
     * 删除金刚位
     * 对应 FeignClient 方法: remove(Long id)
     */
    public Boolean deleteWithValidByIds(List<Long> ids, Boolean isValid) {
        // 由于FeignClient只支持单个删除，这里循环调用
        for (Long id : ids) {
            R<Void> result = diamondPositionFeignClient.remove(id);
            if (result.getCode() != 200) {
                return false;
            }
        }
        return true;
    }

    /**
     * 更新金刚位排序
     * 对应 FeignClient 方法: updateSort(List<OtoDiamondPositionBo> sortList)
     */
    public Boolean updateSort(List<OtoDiamondPositionBo> sortList) {
        R<Void> result = diamondPositionFeignClient.updateSort(sortList);
        return result.getCode() == 200;
    }

    /**
     * 启用/禁用金刚位
     * 对应 FeignClient 方法: updateStatus(Long id, Integer status)
     */
    public Boolean updateStatus(Long id, Integer status) {
        R<Void> result = diamondPositionFeignClient.updateStatus(id, status);
        return result.getCode() == 200;
    }

    /**
     * 批量更新状态
     * 对应 FeignClient 方法: batchUpdateStatus(Map<String, Object> request)
     */
    public Boolean batchUpdateStatus(Map<String, Object> request) {
        R<Void> result = diamondPositionFeignClient.batchUpdateStatus(request);
        return result.getCode() == 200;
    }
}
