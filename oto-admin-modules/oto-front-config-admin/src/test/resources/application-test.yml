# 测试环境配置
server:
  port: 0  # 随机端口

# 管理端口配置（测试环境）
management:
  server:
    port: 0  # 随机端口
    address: 127.0.0.1
  endpoints:
    web:
      exposure:
        include: 'health,info'
      base-path: /actuator
  endpoint:
    health:
      show-details: always

# 测试环境的安全配置
oto:
  internal:
    server:
      enabled: false  # 测试环境禁用内部服务端口
    security:
      enabled: true
      feign-client:
        enabled: true
        direct-access: false
        only-service-layer: true
      monitoring:
        enabled: true
        interval: 60000  # 1分钟检查一次（测试环境更频繁）
        health-check: true
        metrics: true
      audit:
        enabled: true
        log-level: "DEBUG"
        detailed: true
      event-listener:
        enabled: true
        async: false  # 测试环境同步处理
        queue-size: 100
        thread-pool-size: 1

# 日志配置
logging:
  level:
    com.oto.admin.frontconfig: DEBUG
    com.oto.admin.frontconfig.security: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Spring 配置
spring:
  application:
    name: OTO-Admin-Test
  profiles:
    active: test
  
  # 数据源配置（测试环境使用内存数据库）
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    
  # JPA 配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true

# 安全配置（测试环境）
security:
  excludes:
    - "/**"  # 测试环境排除所有安全检查

# Sa-Token 配置（测试环境）
sa-token:
  token-name: Authorization
  is-concurrent: true
  is-share: true
  jwt-secret-key: test-secret-key
  check-same-token: false  # 测试环境禁用服务间认证

# 禁用不必要的自动配置
spring:
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
      - org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration
