<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>oto-admin-modules</artifactId>
        <groupId>com.oto</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>oto-front-config-admin</artifactId>

    <description>
        oto-front-config-admin 前端配置管理模块
    </description>

    <dependencies>
        <!-- 通用工具 -->
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-core</artifactId>
        </dependency>

        <!-- Web相关 -->
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-web</artifactId>
        </dependency>

        <!-- 权限认证 -->
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-satoken</artifactId>
        </dependency>

        <!-- 日志记录 -->
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-log</artifactId>
        </dependency>

        <!-- 远程调用 -->
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-http-client</artifactId>
        </dependency>

        <!-- 参数校验 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- 远程前端API -->
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>remote-front-api</artifactId>
        </dependency>

        <!-- 通用远程调用领域模型 -->
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-remote-domain</artifactId>
        </dependency>

        <!-- 数据库相关 -->
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-mybatis</artifactId>
        </dependency>

        <!-- Excel工具 -->
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-excel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.oto</groupId>
            <artifactId>oto-common-idempotent</artifactId>
        </dependency>
    </dependencies>

</project>
