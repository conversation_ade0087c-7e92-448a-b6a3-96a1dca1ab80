# 动态端口配置说明

## 📋 概述

本模块实现了动态端口配置机制，根据主端口自动计算其他服务端口，避免硬编码端口号，提高配置的灵活性。

## 🎯 端口计算规则

### 基础配置
- **主端口 (Main Port)**: 应用的主要业务端口，默认 8080
- **端口偏移量 (Port Offset)**: 用于计算其他端口的偏移量，默认 10

### 计算公式
```
管理端口 = 主端口 + 偏移量 - 8
内部端口 = 主端口 + 偏移量 - 7
```

### 默认端口分配
```
主端口:   8080 (业务接口)
管理端口: 8082 (8080 + 10 - 8 = 8082, Actuator 监控)
内部端口: 8083 (8080 + 10 - 7 = 8083, 内部服务)
```

## ⚙️ 配置方式

### application.yml 配置
```yaml
# 主服务端口
server:
  port: 8080

# 内部服务配置
oto:
  internal:
    server:
      enabled: true
      main-port: ${server.port:8080}  # 引用主端口
      port-offset: 10                 # 端口偏移量
      address: 127.0.0.1              # 只绑定本地地址
      context-path: /internal
```

### 环境变量配置
```bash
# 设置主端口
export SERVER_PORT=9080

# 设置端口偏移量
export OTO_INTERNAL_SERVER_PORT_OFFSET=20
```

### 命令行参数配置
```bash
java -jar app.jar --server.port=9080 --oto.internal.server.port-offset=20
```

## 🔧 端口计算示例

### 示例 1: 默认配置
```
主端口: 8080
偏移量: 10
管理端口: 8080 + 10 - 8 = 8082
内部端口: 8080 + 10 - 7 = 8083
```

### 示例 2: 自定义主端口
```
主端口: 9080
偏移量: 10
管理端口: 9080 + 10 - 8 = 9082
内部端口: 9080 + 10 - 7 = 9083
```

### 示例 3: 自定义偏移量
```
主端口: 8080
偏移量: 20
管理端口: 8080 + 20 - 8 = 8092
内部端口: 8080 + 20 - 7 = 8093
```

## 🔒 安全验证

### 端口验证逻辑
```java
// 内部服务端口验证
int managementPort = properties.getManagementPort();
int internalPort = properties.getInternalPort();
boolean isInternalPort = serverPort == managementPort || serverPort == internalPort;

// 主业务端口验证
int mainPort = properties.getMainPort();
boolean isMainPort = serverPort == mainPort;
```

### 权限验证
- **内部端口**: 需要 `INTERNAL_SERVICE` 角色
- **主业务端口**: API 需要登录，管理接口需要 `ADMIN` 角色

## 🚀 使用场景

### 1. 开发环境
```yaml
# 开发环境使用默认端口
oto:
  internal:
    server:
      main-port: 8080
      port-offset: 10
```

### 2. 测试环境
```yaml
# 测试环境避免端口冲突
oto:
  internal:
    server:
      main-port: 9080
      port-offset: 10
```

### 3. 生产环境
```yaml
# 生产环境使用更大的偏移量
oto:
  internal:
    server:
      main-port: 8080
      port-offset: 100
```

## 📊 配置验证

### 自动验证规则
1. **端口范围验证**: 所有端口必须在 1-65535 范围内
2. **端口冲突检查**: 主端口、管理端口、内部端口不能相同
3. **地址安全检查**: 内部端口只能绑定本地地址

### 验证日志示例
```
✅ 内部服务配置验证通过:
  - 主端口: 8080
  - 管理端口: 8082
  - 内部端口: 8083
  - 地址: 127.0.0.1
  - 上下文路径: /internal
  - 端口偏移量: 10
```

## 🔧 故障排除

### 常见问题

#### 1. 端口被占用
```
错误: Address already in use: bind
解决: 修改主端口或端口偏移量
```

#### 2. 端口计算错误
```
错误: 端口冲突: 主端口=8080, 管理端口=8080, 内部端口=8080
解决: 检查端口偏移量配置
```

#### 3. 权限验证失败
```
错误: 只能通过内部端口访问，当前端口: 8080, 允许端口: 8082, 8083
解决: 确保请求通过正确的端口访问
```

## 🎯 最佳实践

### 1. 端口规划
- 为不同环境预留端口范围
- 使用有意义的端口偏移量
- 避免与系统端口冲突

### 2. 配置管理
- 使用环境变量管理不同环境的端口
- 在配置文件中添加详细注释
- 定期检查端口使用情况

### 3. 安全考虑
- 内部端口只绑定本地地址
- 使用防火墙限制端口访问
- 定期审查端口配置

## 📈 扩展性

### 添加新的服务端口
```java
// 在 InternalServerProperties 中添加新的端口计算方法
public int getNewServicePort() {
    return mainPort + portOffset - 6;  // 新服务端口
}
```

### 支持多个偏移量
```yaml
oto:
  internal:
    server:
      main-port: 8080
      management-offset: 2   # 管理端口偏移
      internal-offset: 3     # 内部端口偏移
```

这种动态端口配置机制提供了灵活性和可维护性，避免了硬编码端口号的问题，特别适合多环境部署和容器化场景。
