package com.oto.common.satoken.config;

import cn.dev33.satoken.filter.SaServletFilter;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.same.SaSameUtil;
import cn.dev33.satoken.util.SaResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Sa-Token Same-Token 自动配置类
 * 根据配置自动创建 Same-Token 验证过滤器
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(SaTokenSameTokenProperties.class)
public class SaTokenSameTokenAutoConfiguration {

    /**
     * 后管服务 Same-Token 过滤器
     * 保护 /admin/api/** 路径
     */
    @Bean
    @ConditionalOnProperty(name = "sa-token.same-token.admin.enabled", havingValue = "true")
    public SaServletFilter adminSameTokenFilter(SaTokenSameTokenProperties properties) {
        log.info("启用后管服务 Same-Token 验证，保护路径: {}", properties.getAdmin().getProtectedPaths());
        
        return createSameTokenFilter(
            properties.getAdmin().getProtectedPaths(),
            properties.getAdmin().getExcludePaths(),
            "后管服务"
        );
    }

    /**
     * 前台服务 Same-Token 过滤器
     * 保护 /app/api/** 路径
     */
    @Bean
    @ConditionalOnProperty(name = "sa-token.same-token.front.enabled", havingValue = "true")
    public SaServletFilter frontSameTokenFilter(SaTokenSameTokenProperties properties) {
        log.info("启用前台服务 Same-Token 验证，保护路径: {}", properties.getFront().getProtectedPaths());
        
        return createSameTokenFilter(
            properties.getFront().getProtectedPaths(),
            properties.getFront().getExcludePaths(),
            "前台服务"
        );
    }

    /**
     * 创建 Same-Token 验证过滤器
     *
     * @param protectedPaths 需要保护的路径列表
     * @param excludePaths   需要排除的路径列表
     * @param serviceName    服务名称（用于日志）
     * @return SaServletFilter
     */
    private SaServletFilter createSameTokenFilter(String[] protectedPaths, String[] excludePaths, String serviceName) {
        SaServletFilter filter = new SaServletFilter()
                .addInclude("/**");

        // 添加排除路径
        for (String excludePath : excludePaths) {
            filter.addExclude(excludePath);
        }

        // 设置认证逻辑
        filter.setAuth(obj -> {
            for (String protectedPath : protectedPaths) {
                SaRouter.match(protectedPath, () -> {
                    log.debug("{}验证服务间调用 Same-Token，请求路径: {}", serviceName, protectedPath);
                    SaSameUtil.checkCurrentRequestToken();
                    log.debug("{}Same-Token 验证通过", serviceName);
                });
            }
        });

        // 设置错误处理
        filter.setError(e -> {
            log.warn("{}服务间认证失败: {}", serviceName, e.getMessage());
            return SaResult.error("服务间认证失败: " + e.getMessage());
        });

        return filter;
    }
}
