package com.oto.common.satoken.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Sa-Token Same-Token 配置属性
 *
 * <AUTHOR>
 * @date 2025-08-31
 */
@Data
@ConfigurationProperties(prefix = "sa-token.same-token")
public class SaTokenSameTokenProperties {

    /**
     * 后管服务配置
     */
    private ServiceConfig admin = new ServiceConfig();

    /**
     * 前台服务配置
     */
    private ServiceConfig front = new ServiceConfig();

    /**
     * 服务配置
     */
    @Data
    public static class ServiceConfig {
        
        /**
         * 是否启用 Same-Token 验证
         */
        private boolean enabled = false;

        /**
         * 需要保护的路径
         */
        private String[] protectedPaths = {};

        /**
         * 需要排除的路径
         */
        private String[] excludePaths = {
            "/favicon.ico",
            "/actuator/**",
            "/doc.html",
            "/swagger-ui/**",
            "/swagger-resources/**",
            "/webjars/**",
            "/v3/api-docs/**",
            "/druid/**",
            "/captcha/**",
            "/static/**"
        };
    }
}
