# 同机器部署 HTTPS 自动配置指南

## 🎯 **概述**

当 oto-admin 和 oto-front 两个服务部署在同一台机器上时，我们提供了自动生成 HTTPS 证书的解决方案，无需手动配置复杂的 SSL 证书。

## 🔒 **自动 HTTPS 证书生成方案**

### 方案特点
- ✅ **自动检测** - 检测同机器部署环境
- ✅ **自动生成** - 自动生成自签名证书
- ✅ **自动配置** - 自动应用 SSL 配置
- ✅ **自动更新** - 证书过期前自动提醒
- ✅ **零配置** - 开箱即用，无需手动干预

## 🚀 **快速开始**

### 1. 启用自动 HTTPS（默认已启用）

在 `application.yml` 中确认配置：

```yaml
oto:
  common:
    http:
      ssl:
        auto-generate: true  # 启用自动生成（默认true）
```

### 2. 启动服务

```bash
# 启动 oto-admin（会自动生成证书并启用 HTTPS）
cd oto-admin
mvn spring-boot:run

# 启动 oto-front（会使用相同的证书）
cd oto-front
mvn spring-boot:run
```

### 3. 访问服务

```bash
# oto-admin 服务
https://localhost:8443

# oto-front 服务  
https://localhost:8444
```

**注意**: 浏览器会显示安全警告，这是正常的，点击"高级" → "继续访问"即可。

## 🔧 **详细配置**

### 完整的 SSL 配置

```yaml
# oto-admin/src/main/resources/application.yml
server:
  port: 8443  # HTTPS 端口
  ssl:
    enabled: true
    key-store: ${user.home}/.oto/ssl/auto-generated.p12
    key-store-password: oto-ssl-2025
    key-store-type: PKCS12
    key-alias: oto-service

oto:
  services:
    admin:
      base-url: https://localhost:8443  # 使用 HTTPS
    front:
      base-url: https://localhost:8444  # 使用 HTTPS
  
  common:
    http:
      ssl:
        auto-generate: true
        keystore-path: ${user.home}/.oto/ssl/auto-generated.p12
        keystore-password: oto-ssl-2025
        certificate-alias: oto-service
        validity-days: 365
        subject: "CN=localhost, OU=OTO-System, O=OTO, L=Shanghai, ST=Shanghai, C=CN"
        subject-alternative-names:
          - "DNS:localhost"
          - "IP:127.0.0.1"
          - "IP:0.0.0.0"
      
      multi-port:
        enabled: true
        main-port: 8443  # HTTPS 主端口
        port-offset: 10  # 远程端口 8453
```

### FeignClient SSL 配置

```yaml
# 自动配置 FeignClient 信任自签名证书
feign:
  httpclient:
    ssl:
      trust-store: ${user.home}/.oto/ssl/auto-generated.p12
      trust-store-password: oto-ssl-2025
      trust-store-type: PKCS12
      trust-all-certs: true  # 信任所有证书（仅开发环境）
```

## 🛠️ **手动生成证书**

如果需要手动生成证书，可以使用提供的脚本：

```bash
# 使用默认配置生成证书
chmod +x oto-common-modules/oto-common-http-client/src/main/resources/ssl/generate-ssl.sh
./oto-common-modules/oto-common-http-client/src/main/resources/ssl/generate-ssl.sh

# 自定义路径和密码
./generate-ssl.sh /path/to/keystore.p12 your-password

# 查看生成的证书信息
keytool -list -v -keystore ~/.oto/ssl/auto-generated.p12 -storepass oto-ssl-2025
```

## 📊 **不同部署场景的配置**

### 场景1: 同机器开发环境

```yaml
# 自动检测并启用 HTTPS
oto:
  common:
    http:
      ssl:
        auto-generate: true  # ✅ 自动生成

server:
  port: 8443  # HTTPS 端口
```

### 场景2: 生产环境（使用正式证书）

```yaml
# 禁用自动生成，使用正式证书
oto:
  common:
    http:
      ssl:
        auto-generate: false  # ❌ 禁用自动生成

server:
  port: 443
  ssl:
    enabled: true
    key-store: /etc/ssl/certs/production.p12
    key-store-password: ${SSL_PASSWORD}
    key-store-type: PKCS12
```

### 场景3: Docker 容器部署

```yaml
# Docker 环境自动生成
oto:
  common:
    http:
      ssl:
        auto-generate: true
        keystore-path: /app/ssl/container.p12  # 容器内路径

volumes:
  - ssl-certs:/app/ssl  # 持久化证书
```

## 🔍 **证书管理**

### 证书位置

```bash
# 默认证书位置
~/.oto/ssl/auto-generated.p12

# 配置文件
~/.oto/ssl/ssl-config.yml

# 备份文件
~/.oto/ssl/auto-generated.p12.backup.20250831_143022
```

### 证书信息查看

```bash
# 查看证书详情
keytool -list -v -keystore ~/.oto/ssl/auto-generated.p12 -storepass oto-ssl-2025

# 检查证书有效期
keytool -list -keystore ~/.oto/ssl/auto-generated.p12 -storepass oto-ssl-2025 | grep "Valid until"

# 导出证书（用于浏览器导入）
keytool -export -alias oto-service -keystore ~/.oto/ssl/auto-generated.p12 -storepass oto-ssl-2025 -file oto-cert.crt
```

### 证书更新

```bash
# 删除旧证书，重新生成
rm ~/.oto/ssl/auto-generated.p12
# 重启服务，会自动生成新证书

# 或者手动生成
./generate-ssl.sh ~/.oto/ssl/auto-generated.p12 oto-ssl-2025
```

## 🚨 **安全注意事项**

### ✅ **适用场景**
- 开发环境
- 测试环境  
- 内网部署
- 同机器服务间通信

### ❌ **不适用场景**
- 生产环境（建议使用正式 CA 证书）
- 公网访问（浏览器会警告）
- 对外提供的 API 服务

### 🔒 **安全建议**

1. **生产环境使用正式证书**
```yaml
# 生产环境配置
spring:
  profiles: prod
  
oto:
  common:
    http:
      ssl:
        auto-generate: false  # 禁用自动生成
```

2. **定期更新证书**
```bash
# 设置定时任务，定期检查证书有效期
0 0 * * * /path/to/check-cert-expiry.sh
```

3. **保护证书文件**
```bash
# 设置适当的文件权限
chmod 600 ~/.oto/ssl/auto-generated.p12
chown $USER:$USER ~/.oto/ssl/auto-generated.p12
```

## 🐛 **故障排除**

### 问题1: 证书生成失败

```bash
# 检查 Java 环境
java -version
which keytool

# 检查目录权限
ls -la ~/.oto/ssl/

# 手动生成测试
keytool -genkeypair -alias test -keyalg RSA -keystore test.p12 -storetype PKCS12
```

### 问题2: HTTPS 连接失败

```bash
# 检查端口是否监听
netstat -tlnp | grep 8443

# 检查证书是否有效
openssl s_client -connect localhost:8443 -servername localhost

# 检查 FeignClient 配置
curl -k https://localhost:8443/actuator/health
```

### 问题3: 浏览器安全警告

```bash
# 导出证书并导入浏览器
keytool -export -alias oto-service -keystore ~/.oto/ssl/auto-generated.p12 -storepass oto-ssl-2025 -file oto-cert.crt

# Chrome: 设置 → 隐私设置和安全性 → 安全 → 管理证书 → 导入
# Firefox: 设置 → 隐私与安全 → 证书 → 查看证书 → 导入
```

## 📈 **性能优化**

### SSL 性能优化

```yaml
server:
  ssl:
    enabled: true
    # 启用 HTTP/2
    http2:
      enabled: true
    # SSL 会话缓存
    session:
      cache-size: 10000
      timeout: 300s
```

### JVM SSL 优化

```bash
# JVM 启动参数
-Djava.security.egd=file:/dev/./urandom
-Djavax.net.ssl.trustStore=~/.oto/ssl/auto-generated.p12
-Djavax.net.ssl.trustStorePassword=oto-ssl-2025
```

## 🎉 **总结**

自动 HTTPS 证书生成方案为同机器部署提供了便捷的 SSL 配置：

1. **零配置** - 开箱即用
2. **自动检测** - 智能识别部署环境  
3. **安全可靠** - 符合开发和测试需求
4. **易于管理** - 提供完整的管理工具

对于生产环境，建议使用正式的 CA 证书以确保最高的安全性和兼容性。
