# 🤖 AI编程助手开发规范文档

## 📋 文档概述

本文档专门为AI编程助手制定，旨在确保AI在生成代码时严格遵循项目架构规范，防止乱写代码、乱引入依赖等问题，保证代码质量和系统稳定性。

**文档版本**: v1.0  
**创建日期**: 2025-01-31  
**适用范围**: 所有AI编程助手  
**基础框架**: RuoYi-Vue-Plus  
**技术栈**: Spring Boot 3.x + MyBatis-Plus + Redis + MySQL + Sa-Token

---

## 🏗️ 项目架构约束

### 1.1 双入口架构设计（严格遵循）

```
oto-home/
├── oto-admin/              # 后台管理入口 (端口:8080)
├── oto-front/              # 会员业务入口 (端口:8081)
├── oto-admin-modules/      # 后台管理业务模块
├── oto-front-modules/      # 会员业务模块
├── oto-common/             # 公共基础组件
├── oto-common-modules/     # 公共业务组件
└── oto-extend/             # 扩展组件
```

**🚨 严格禁止的操作**:
- ❌ 跨模块直接依赖 (admin-modules ↔ front-modules)
- ❌ 反向依赖 (业务模块依赖入口模块)
- ❌ 在业务模块中重复造轮子
- ❌ 绕过公共组件直接实现功能

### 1.2 模块依赖规则

**✅ 允许的依赖关系**:
```
oto-admin → oto-admin-modules → oto-common
oto-front → oto-front-modules → oto-common
业务模块 → oto-common-modules → oto-common
```

**❌ 禁止的依赖关系**:
```
oto-admin-modules ↔ oto-front-modules  # 业务模块互相依赖
oto-common → 业务模块                   # 反向依赖
业务模块 → oto-admin/oto-front         # 业务依赖入口
```

---

## 🎯 代码生成规范

### 2.1 分层架构规范

**Controller层**:
```java
@RestController
@RequestMapping("/system/user")
@RequiredArgsConstructor
@Validated
public class SysUserController extends BaseController {
    
    private final ISysUserService userService;
    
    @SaCheckPermission("system:user:list")
    @GetMapping("/list")
    public TableDataInfo<SysUserVo> list(SysUserBo bo, PageQuery pageQuery) {
        return userService.queryPageList(bo, pageQuery);
    }
}
```

**Service层**:
```java
@Service
@RequiredArgsConstructor
public class SysUserServiceImpl implements ISysUserService {
    
    private final SysUserMapper baseMapper;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(SysUserBo bo) {
        SysUser add = MapstructUtils.convert(bo, SysUser.class);
        validEntityBeforeSave(add);
        return baseMapper.insert(add) > 0;
    }
}
```

### 2.2 数据对象使用规范

**Entity（实体类）**:
```java
@Data
@TableName("sys_user")
public class SysUser extends BaseEntity {
    
    @TableId(value = "user_id", type = IdType.ASSIGN_ID)
    private Long userId;
    
    @TableField("user_name")
    private String userName;
    
    // 敏感字段加密
    @EncryptField(algorithm = EncryptAlgorithm.AES)
    private String phone;
}
```

**BO（Business Object）**:
```java
@Data
@EqualsAndHashCode(callSuper = true)
public class SysUserBo extends BaseEntity {
    
    @NotBlank(message = "用户名不能为空", groups = {AddGroup.class, EditGroup.class})
    private String userName;
    
    @NotBlank(message = "手机号不能为空", groups = {AddGroup.class})
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
}
```

**VO（View Object）**:
```java
@Data
@AutoMapper(target = SysUser.class)
public class SysUserVo implements Serializable {
    
    private Long userId;
    private String userName;
    
    // 敏感数据脱敏
    @Sensitive(strategy = SensitiveStrategy.PHONE)
    private String phone;
}
```

---

## 🔧 技术组件使用规范

### 3.1 必须使用的公共组件

**数据库操作**:
```java
// ✅ 正确：使用MyBatis-Plus
@Mapper
public interface SysUserMapper extends BaseMapperPlus<SysUser, SysUserVo> {
}

// ❌ 错误：直接使用JDBC
// Connection conn = DriverManager.getConnection(...)
```

**缓存操作**:
```java
// ✅ 正确：使用框架缓存工具
@Cacheable(cacheNames = CacheNames.SYS_CONFIG, key = "#configKey")
public String getConfigByKey(String configKey) {
    return baseMapper.selectConfigByKey(configKey);
}

// ✅ 正确：使用RedisUtils
RedisUtils.setCacheObject("key", value, Duration.ofMinutes(30));

// ❌ 错误：直接使用Redis客户端
// Jedis jedis = new Jedis("localhost", 6379);
```

**权限验证**:
```java
// ✅ 正确：使用Sa-Token注解
@SaCheckPermission("system:user:add")
@PostMapping
public R<Void> add(@RequestBody SysUserBo bo) {
    return toAjax(userService.insertByBo(bo));
}

// ❌ 错误：自定义权限验证
// if (!hasPermission(userId, "system:user:add")) { ... }
```

### 3.2 异常处理规范

```java
// ✅ 正确：使用ServiceException
if (StringUtils.isBlank(userName)) {
    throw new ServiceException("用户名不能为空");
}

// ✅ 正确：使用Assert工具
Assert.notBlank(userName, () -> new ServiceException("用户名不能为空"));

// ❌ 错误：使用原生异常
// throw new RuntimeException("用户名不能为空");
```

### 3.3 日志记录规范

```java
// ✅ 正确：使用@Log注解
@Log(title = "用户管理", businessType = BusinessType.INSERT)
@PostMapping
public R<Void> add(@RequestBody SysUserBo bo) {
    return toAjax(userService.insertByBo(bo));
}

// ✅ 正确：使用lombok日志
@Slf4j
public class SysUserServiceImpl {
    public void someMethod() {
        log.info("执行用户操作，用户ID: {}", userId);
    }
}
```

---

## 🚨 严格禁止的操作

### 4.1 依赖管理禁令

**❌ 禁止随意添加依赖**:
```xml
<!-- 错误：未经审核添加新依赖 -->
<dependency>
    <groupId>com.alibaba</groupId>
    <artifactId>fastjson</artifactId>
    <version>1.2.83</version>
</dependency>
```

**✅ 正确：使用已有依赖**:
```java
// 使用项目已有的Jackson
String json = JsonUtils.toJsonString(object);
Object obj = JsonUtils.parseObject(json, Class.class);
```

### 4.2 配置管理禁令

**❌ 禁止硬编码配置**:
```java
// 错误示例
String redisHost = "localhost";
int redisPort = 6379;
String dbUrl = "*******************************";
```

**✅ 正确：使用配置文件**:
```java
@Value("${spring.redis.host}")
private String redisHost;

@ConfigurationProperties(prefix = "oto.business")
@Data
public class BusinessProperties {
    private String apiUrl;
    private int timeout;
}
```

### 4.3 安全相关禁令

**❌ 禁止绕过安全验证**:
```java
// 错误：跳过权限验证
// @GetMapping("/sensitive-data")
// public R<List<String>> getSensitiveData() { ... }
```

**✅ 正确：严格权限控制**:
```java
@SaCheckPermission("system:sensitive:view")
@GetMapping("/sensitive-data")
public R<List<String>> getSensitiveData() {
    return R.ok(service.getSensitiveData());
}
```

---

## 📝 代码质量要求

### 5.1 命名规范

```java
// ✅ 正确命名
public class SysUserServiceImpl implements ISysUserService {
    private final SysUserMapper userMapper;
    
    public TableDataInfo<SysUserVo> queryPageList(SysUserBo bo, PageQuery pageQuery) {
        // 实现逻辑
    }
}

// ❌ 错误命名
public class UserSvc {
    private SysUserMapper mapper;
    
    public Object getList(Object param) {
        // 实现逻辑
    }
}
```

### 5.2 注释规范

```java
/**
 * 用户管理Service业务层处理
 *
 * <AUTHOR> Assistant
 * @date 2025-01-31
 */
@Service
@RequiredArgsConstructor
public class SysUserServiceImpl implements ISysUserService {
    
    /**
     * 查询用户分页列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户分页列表
     */
    @Override
    public TableDataInfo<SysUserVo> queryPageList(SysUserBo bo, PageQuery pageQuery) {
        // 实现逻辑
    }
}
```

---

## 🔄 AI助手交互要求

### 6.1 强制交互规则

**⚠️ 重要**: AI助手在执行任务过程中，必须使用 `interactive-feedback` 工具与开发者进行交互，确保：

1. **任务理解确认** - 开始编码前确认需求理解
2. **架构方案确认** - 重要架构决策需要确认
3. **代码生成汇报** - 生成代码后汇报实现方案
4. **问题及时反馈** - 遇到问题立即反馈

### 6.2 交互示例

```javascript
// 任务开始时
interactive_feedback({
    work_summary: "开始实现用户管理功能，将创建Controller、Service、Mapper等分层代码，严格遵循项目架构规范..."
});

// 遇到问题时
interactive_feedback({
    work_summary: "发现需要新增依赖，但根据规范不应随意添加。建议使用现有的工具类实现相同功能..."
});
```

---

## 📊 质量检查清单

### 7.1 代码生成后必检项

- [ ] 是否遵循分层架构规范
- [ ] 是否使用了正确的公共组件
- [ ] 是否添加了必要的权限验证
- [ ] 是否使用了正确的异常处理
- [ ] 是否遵循命名规范
- [ ] 是否添加了完整注释
- [ ] 是否避免了硬编码
- [ ] 是否正确处理了敏感数据

### 7.2 架构合规检查

- [ ] 模块依赖关系是否正确
- [ ] 是否违反了跨模块依赖禁令
- [ ] 是否使用了已有的公共能力
- [ ] 是否符合双入口架构设计

---

## 🎯 总结

AI编程助手必须严格遵循本规范，确保生成的代码：
1. **架构合规** - 符合项目架构设计
2. **组件复用** - 优先使用已有组件
3. **安全可靠** - 遵循安全开发规范
4. **质量可控** - 满足企业级开发标准
5. **可维护性** - 代码清晰易维护

**记住：宁可多问一句，不可错写一行！**

---

## 🛠️ 具体技术实现规范

### 8.1 数据库操作规范

**Mapper接口定义**:
```java
// ✅ 正确：继承BaseMapperPlus
@Mapper
public interface SysUserMapper extends BaseMapperPlus<SysUser, SysUserVo> {

    // 复杂查询使用注解
    @Select("SELECT * FROM sys_user WHERE dept_id = #{deptId} AND status = '0'")
    List<SysUserVo> selectUsersByDept(@Param("deptId") Long deptId);
}

// ❌ 错误：不继承基础Mapper
public interface SysUserMapper {
    // 缺少基础CRUD能力
}
```

**XML映射文件**:
```xml
<!-- 文件位置：resources/mapper/system/SysUserMapper.xml -->
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.oto.system.mapper.SysUserMapper">

    <select id="selectUsersByDept" resultType="com.oto.system.domain.vo.SysUserVo">
        SELECT user_id, user_name, nick_name, phone
        FROM sys_user
        WHERE dept_id = #{deptId}
        AND del_flag = '0'
        AND status = '0'
    </select>

</mapper>
```

### 8.2 缓存使用规范

**Spring Cache注解**:
```java
@Service
public class SysConfigServiceImpl implements ISysConfigService {

    // ✅ 正确：使用缓存注解
    @Cacheable(cacheNames = CacheNames.SYS_CONFIG, key = "#configKey")
    @Override
    public String selectConfigByKey(String configKey) {
        return baseMapper.selectConfigByKey(configKey);
    }

    @CacheEvict(cacheNames = CacheNames.SYS_CONFIG, key = "#configKey")
    @Override
    public void updateConfig(String configKey, String configValue) {
        // 更新逻辑
    }
}
```

**Redis工具类使用**:
```java
// ✅ 正确：使用RedisUtils
public class TokenService {

    public void saveToken(String tokenKey, LoginUser loginUser) {
        RedisUtils.setCacheObject(tokenKey, loginUser, Duration.ofMinutes(30));
    }

    public LoginUser getLoginUser(String tokenKey) {
        return RedisUtils.getCacheObject(tokenKey);
    }

    public void deleteToken(String tokenKey) {
        RedisUtils.deleteObject(tokenKey);
    }
}
```

### 8.3 远程调用规范

**声明式远程服务**:
```java
// ✅ 正确：使用声明式远程服务
@RemoteService("${oto.services.member.base-url}")
public interface MemberRemoteService {

    @GetApi("/member/info/{id}")
    Map<String, Object> getMemberInfo(@PathVariable("id") Long id);

    @PostApi("/member/register")
    Map<String, Object> registerMember(@RequestBody Map<String, Object> memberData);
}

// 使用方式
@Service
@RequiredArgsConstructor
public class AdminMemberService {

    private final MemberRemoteService memberRemoteService;

    public MemberInfo getMemberInfo(Long memberId) {
        Map<String, Object> result = memberRemoteService.getMemberInfo(memberId);
        return MapstructUtils.convert(result, MemberInfo.class);
    }
}
```

### 8.4 配置管理规范

**配置类定义**:
```java
@Data
@ConfigurationProperties(prefix = "oto.business")
@Component
public class BusinessProperties {

    /**
     * API接口地址
     */
    private String apiUrl;

    /**
     * 超时时间（秒）
     */
    private Integer timeout = 30;

    /**
     * 重试次数
     */
    private Integer retryCount = 3;

    /**
     * 是否启用
     */
    private Boolean enabled = true;
}
```

**配置文件结构**:
```yaml
# application.yml
oto:
  business:
    api-url: ${BUSINESS_API_URL:http://localhost:8080}
    timeout: ${BUSINESS_TIMEOUT:30}
    retry-count: ${BUSINESS_RETRY:3}
    enabled: ${BUSINESS_ENABLED:true}

  # 安全配置
  security:
    jwt:
      secret: ${JWT_SECRET:your-secret-key}
      expire-time: ${JWT_EXPIRE:7200}

  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 600
      key-prefix: "oto:"
```

### 8.5 安全开发规范

**权限验证**:
```java
@RestController
@RequestMapping("/system/user")
@RequiredArgsConstructor
public class SysUserController extends BaseController {

    // ✅ 正确：细粒度权限控制
    @SaCheckPermission("system:user:list")
    @GetMapping("/list")
    public TableDataInfo<SysUserVo> list(SysUserBo bo, PageQuery pageQuery) {
        return userService.queryPageList(bo, pageQuery);
    }

    @SaCheckPermission("system:user:add")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysUserBo bo) {
        return toAjax(userService.insertByBo(bo));
    }
}
```

**数据脱敏**:
```java
@Data
public class SysUserVo implements Serializable {

    private Long userId;
    private String userName;

    // ✅ 正确：敏感数据脱敏
    @Sensitive(strategy = SensitiveStrategy.PHONE)
    private String phone;

    @Sensitive(strategy = SensitiveStrategy.EMAIL)
    private String email;

    @Sensitive(strategy = SensitiveStrategy.ID_CARD)
    private String idCard;
}
```

**数据加密**:
```java
@Data
@TableName("sys_user")
public class SysUser extends BaseEntity {

    @TableId(value = "user_id", type = IdType.ASSIGN_ID)
    private Long userId;

    // ✅ 正确：敏感字段加密存储
    @EncryptField(algorithm = EncryptAlgorithm.AES)
    private String phone;

    @EncryptField(algorithm = EncryptAlgorithm.AES)
    private String idCard;
}
```

### 8.6 数据验证规范

**Bean Validation使用**:
```java
@Data
public class SysUserBo extends BaseEntity {

    @NotBlank(message = "用户名不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(min = 2, max = 20, message = "用户名长度必须在2-20个字符之间")
    private String userName;

    @NotBlank(message = "手机号不能为空", groups = {AddGroup.class})
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    @Email(message = "邮箱格式不正确")
    private String email;

    @NotNull(message = "状态不能为空", groups = {AddGroup.class, EditGroup.class})
    @Range(min = 0, max = 1, message = "状态值只能是0或1")
    private Integer status;
}
```

**自定义验证器**:
```java
// ✅ 正确：使用ValidatorUtils
public class SysUserServiceImpl implements ISysUserService {

    private void validEntityBeforeSave(SysUser entity) {
        // 使用框架验证工具
        ValidatorUtils.validate(entity, AddGroup.class);

        // 业务规则验证
        if (checkUserNameUnique(entity.getUserName())) {
            throw new ServiceException("用户名已存在");
        }
    }
}
```

---

## 🔍 常见错误与正确示例

### 9.1 依赖注入错误

**❌ 错误示例**:
```java
@Service
public class SysUserServiceImpl {

    // 错误：使用@Autowired字段注入
    @Autowired
    private SysUserMapper userMapper;

    // 错误：没有final修饰
    private ISysRoleService roleService;
}
```

**✅ 正确示例**:
```java
@Service
@RequiredArgsConstructor  // 使用lombok构造器注入
public class SysUserServiceImpl implements ISysUserService {

    // 正确：final字段 + 构造器注入
    private final SysUserMapper userMapper;
    private final ISysRoleService roleService;
}
```

### 9.2 异常处理错误

**❌ 错误示例**:
```java
public void updateUser(SysUserBo bo) {
    try {
        // 业务逻辑
        userMapper.updateById(user);
    } catch (Exception e) {
        // 错误：吞掉异常
        e.printStackTrace();
        return;
    }
}
```

**✅ 正确示例**:
```java
public void updateUser(SysUserBo bo) {
    try {
        // 业务逻辑
        userMapper.updateById(user);
    } catch (Exception e) {
        // 正确：记录日志并抛出业务异常
        log.error("更新用户失败，用户ID: {}", bo.getUserId(), e);
        throw new ServiceException("更新用户失败");
    }
}
```

### 9.3 事务处理错误

**❌ 错误示例**:
```java
@RestController
public class SysUserController {

    // 错误：在Controller层使用事务
    @Transactional
    @PostMapping("/user")
    public R<Void> addUser(@RequestBody SysUserBo bo) {
        return toAjax(userService.insertByBo(bo));
    }
}
```

**✅ 正确示例**:
```java
@Service
public class SysUserServiceImpl implements ISysUserService {

    // 正确：在Service层使用事务
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(SysUserBo bo) {
        SysUser user = MapstructUtils.convert(bo, SysUser.class);
        validEntityBeforeSave(user);
        return baseMapper.insert(user) > 0;
    }
}
```

---

## 📚 参考资源

### 10.1 项目内规范文档
- 📄 [项目开发规范](.trae/rules/project_rules.md)
- 📄 [API开发规范](.trae/rules/backend/api-standards.md)
- 📄 [数据库设计规范](.trae/rules/backend/database-standards.md)
- 📄 [安全开发规范](.trae/rules/backend/security-standards.md)

### 10.2 技术组件文档
- 📄 [MyBatis-Plus官方文档](https://baomidou.com/)
- 📄 [Sa-Token官方文档](https://sa-token.cc/)
- 📄 [RuoYi-Vue-Plus文档](https://plus-doc.dromara.org/)

### 10.3 代码示例参考
- 📁 `oto-admin-modules/oto-admin-demo/` - 标准代码示例
- 📁 `oto-common/` - 公共组件使用示例
- 📁 `oto-front-modules/oto-front-system/` - 前端业务示例

---

## ⚡ 快速检查清单

在生成任何代码前，AI助手必须确认：

### 基础检查
- [ ] 确认目标模块归属（admin-modules vs front-modules）
- [ ] 确认是否有现成的公共组件可用
- [ ] 确认是否需要新增依赖（需特别谨慎）
- [ ] 确认权限验证要求

### 代码质量检查
- [ ] 分层架构是否正确
- [ ] 命名是否规范
- [ ] 注释是否完整
- [ ] 异常处理是否合理
- [ ] 事务控制是否正确

### 安全合规检查
- [ ] 敏感数据是否加密/脱敏
- [ ] 权限验证是否完整
- [ ] 输入验证是否充分
- [ ] 日志记录是否合规

**最终原则：当有任何疑问时，立即使用 interactive-feedback 与开发者确认！**
